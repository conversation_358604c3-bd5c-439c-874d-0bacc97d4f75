package com.rutong.medical.admin.constant;

import lombok.Getter;

/**
 * @ClassName AlarmTypeEnum
 * @Description
 * <AUTHOR>
 * @Date 2025/7/23 17:03
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Getter
public enum AlarmTypeEnum {

    LOW_VOLTAGE(1, "低电量报警"),
    KEY_PRESS(2, "呼叫报警"),
    PRE_CHEAR(3, "防拆报警"),
    INFRARED_INTRUSION(4, "红外入侵报警"),
    ENTER_GEOFENCE(5, "进入围栏"),
    EXITGEOFENCE(6, "离开围栏");


    private Integer code;
    private String name;

    AlarmTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        for (AlarmTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }


}
