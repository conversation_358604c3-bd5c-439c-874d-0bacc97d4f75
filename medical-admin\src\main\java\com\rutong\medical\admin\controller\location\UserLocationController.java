package com.rutong.medical.admin.controller.location;

import com.rutong.medical.admin.service.location.UserLocationService;
import com.rutong.medical.admin.vo.location.UserLocationVO;
import com.rutong.medical.admin.vo.location.userLocationIndexStatVO;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName UserLocationController
 * @Description 人员定位
 * <AUTHOR>
 * @Date 2025/7/22 13:49
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@RestController
@RequestMapping("/location/")
@Api(tags = "人员定位")
@AllArgsConstructor
public class UserLocationController {

    private UserLocationService userLocationService;

    /**
     * 单个用户订阅
     * @param userId
     * @return
     */
    @GetMapping("subscriptionSingle")
    @ApiOperation(value = "单个用户订阅")
    public ResponseResult<Void> subscriptionSingle(@RequestParam(required = true) Long userId) {
        return userLocationService.subscriptionSingle(userId) == true ? ResponseResult.success()
                : ResponseResult.error(ErrorCodeEnum.ARGUMENT_PK_ID_NULL);
    }

    /**
     * 获取所有安保人员定位信息
     * @return
     */
    @GetMapping("getSecurityAll")
    @ApiOperation(value = "获取所有安保人员定位信息")
    public ResponseResult<List<UserLocationVO>> getSecurityAll() {
        return ResponseResult.success(userLocationService.getSecurityAll());
    }

    /**
     * 根据楼层获取人员定位信息
     * @param floorId
     * @return
     */
    @GetMapping("getUserLocationByFloor")
    @ApiOperation(value = "根据楼层获取人员定位信息")
    public ResponseResult<List<UserLocationVO>> getUserLocationByFloor(@RequestParam(required = true) Long floorId,String tagCode) {
        return ResponseResult.success(userLocationService.getUserLocationByFloor(floorId,tagCode));
    }


    /**
     * 获取人员定位首页各统计值
     * @return
     */
    @GetMapping("getUserLocationList")
    @ApiOperation(value = "获取人员定位首页各统计值")
    public ResponseResult<userLocationIndexStatVO> getUserLocationList(@RequestParam(required = true) Long floorId) {
        userLocationIndexStatVO userLocationIndexStatVO = new userLocationIndexStatVO();
        userLocationIndexStatVO.setHospitalCount(170);
        userLocationIndexStatVO.setSecurityCount(15);
        userLocationIndexStatVO.setMedicalCount(10);
        userLocationIndexStatVO.setBaseStationCount(20);
        userLocationIndexStatVO.setButtonCount(8);
        return ResponseResult.success(userLocationIndexStatVO);
    }

}
