<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.location.UserTrackTDMapper">


    <select id="selectUserTrack" resultType="com.rutong.medical.admin.entity.location.UserLocation"
            parameterType="com.rutong.medical.admin.dto.location.UserLocationDTO">
        select new_locator_sn,
        point_name,
        user_name,
        employee_number,
        device_sn,
        building_id,
        floor_id,
        point_id,
        building_name,
        floor_name,
        point_name, FIRST (create_time) as create_time
        from alarm_location_detail
        where device_type_code=#{deviceTypeCode} and new_locator_sn>0
        <if test="createTimeStart != null and createTimeStart != ''">
            and create_time >= concat(#{createTimeStart}, ':00')
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''">
            and create_time &lt;= concat(#{createTimeEnd}, ':59')
        </if>
        <if test="userCondition != null and userCondition != ''">
            and (user_name=#{userCondition} or employee_number=#{userCondition} or device_sn=#{userCondition})
        </if>
        group by new_locator_sn, point_name, user_name, employee_number, device_sn, building_id, floor_id, point_id,
        building_name, floor_name, point_name
        order by create_time asc
    </select>
</mapper>
