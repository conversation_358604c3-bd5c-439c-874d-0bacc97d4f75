package com.rutong.medical.admin.vo.location;

import com.rutong.medical.admin.entity.monitor.DeviceMonitor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName UserLocationVO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/22 15:50
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@ApiModel("人员定位")
@Data
public class UserLocationVO {

    @ApiModelProperty(value = "设备类型")
    private String deviceTypeCode;

    @ApiModelProperty(value = "设备Sn")
    private String deviceSn;

    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @ApiModelProperty(value = "是否报警")
    private Byte isAlarm;

    @ApiModelProperty(value = "是否按键")
    private Byte isKey;

    @ApiModelProperty(value = "报警记录表ID")
    private Long alarmDetailId;

    @ApiModelProperty(value = "报警类型")
    private Byte alarmType = 0;

    @ApiModelProperty(value = "新基站编号")
    private String newLocatorSn;

    @ApiModelProperty(value = "旧基站编号")
    private String oldLocatorSn;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "当前基站编号")
    private String baseStationSn;

    @ApiModelProperty(value = "楼栋ID")
    private Long buildingId;

    @ApiModelProperty(value = "楼层ID")
    private Long floorId;

    @ApiModelProperty(value = "点位ID")
    private Long pointId;

    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;

    @ApiModelProperty(value = "楼层名称")
    private String floorName;

    @ApiModelProperty(value = "点位名称")
    private String pointName;

    @ApiModelProperty(value = "监控list")
    private List<DeviceMonitor> deviceMonitorList;
}
