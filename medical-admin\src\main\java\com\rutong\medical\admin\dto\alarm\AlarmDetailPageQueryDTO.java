package com.rutong.medical.admin.dto.alarm;

import com.soft.common.core.object.MyPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName AlarmDetailPageQueryDTO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/28 20:27
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */

@ApiModel("AlarmDetailPageQueryDTO")
@Data
public class AlarmDetailPageQueryDTO extends MyPageParam {

    @ApiModelProperty(value = "设备条件：设备SN、设备名称")
    private String deviceCondition;

    @ApiModelProperty(value = "空间表ID")
    private Long spaceId;

    @ApiModelProperty(value = "确认状态")
    private Byte disposeState;

    @ApiModelProperty(value = "报警开始时间")
    private String alarmDateStart;

    @ApiModelProperty(value = "报警结束时间")
    private String alarmDateEnd;

    @ApiModelProperty(value = "业务系统编号")
    private String businessCode;

    @ApiModelProperty(value = "设备分类code")
    private String deviceTypeCode;
}
