package com.rutong.medical.admin.job;

import com.rutong.medical.admin.entity.device.DeviceArm;
import com.rutong.medical.admin.mapper.device.DeviceArmMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.List;

/**
 * 设备布防定时任务
 *
 * <AUTHOR>
 * @Date 2025-08-01
 */
@Component
public class DeviceArmJob {

//    private static final Logger log = LoggerFactory.getLogger(DeviceArmJob.class);

    @Autowired
    private DeviceArmMapper deviceArmMapper;

    /**
     * 每分钟检查一次布防状态，并更新字段
     */
    @Scheduled(cron = "0 * * * * ?")
//    @XxlJob("autoDeviceArmHandler")
    public void checkAndUpdateArmStatus() {
        // 获取所有需要检查的设备布防记录
        List<DeviceArm> deviceArms = deviceArmMapper.selectList(null);
        LocalTime now = LocalTime.now();
        for (DeviceArm deviceArm : deviceArms) {
            // 检查是否有设置布防时间段
            if (deviceArm.getStartTime() != null && deviceArm.getEndTime() != null) {
                boolean shouldBeArmed = isTimeInRange(now, deviceArm.getStartTime(), deviceArm.getEndTime());
                // 只有当状态需要改变时才更新数据库
                if (shouldBeArmed && deviceArm.getDefenceState() != 1) {
                    deviceArm.setDefenceState(1);
                    deviceArmMapper.updateById(deviceArm);
                } else if (!shouldBeArmed && deviceArm.getDefenceState() != 0) {
                    deviceArm.setDefenceState(0);
                    deviceArmMapper.updateById(deviceArm);
                }
            }
        }
//        log.info("自动布防检查更新完成");
    }

    /**
     * 判断当前时间是否在指定的时间范围内
     *
     * @param currentTime
     * @param startTime
     * @param endTime
     * @return 如果在时间范围内返回true，否则返回false
     */
    private boolean isTimeInRange(LocalTime currentTime, LocalTime startTime, LocalTime endTime) {
        // 处理跨天的情况
        if (startTime.isAfter(endTime)) {
            // 跨天时间段
            return !currentTime.isBefore(startTime) || !currentTime.isAfter(endTime);
        } else {
            return !currentTime.isBefore(startTime) && !currentTime.isAfter(endTime);
        }
    }
} 