package com.rutong.medical.admin.vo.device;

import com.rutong.medical.admin.constant.WhetherConstant;
import com.rutong.medical.admin.vo.location.UserLocationVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Date;

/**
 * @ClassName DeviceBusinessVO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/31 15:11
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@ApiModel("DeviceBusinessVO视图对象")
@Data
public class DeviceBusinessVO {

    @ApiModelProperty(value = "设备表ID")
    private Long id;

    @ApiModelProperty(value = "设备分类表ID")
    private Long deviceTerminalTypeId;

    @ApiModelProperty(value = "设备编号")
    private String deviceCode;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "业务系统编号")
    private String businessCode;

    @ApiModelProperty(value = "安装位置")
    private Long spaceId;

    @ApiModelProperty(value = "所属楼层路径")
    private String spacePath;

    @ApiModelProperty(value = "所属楼层全名称")
    private String spaceFullName;

    @ApiModelProperty(value = "经度")
    private Long longitude;

    @ApiModelProperty(value = "纬度")
    private Long latitude;

    @ApiModelProperty(value = "在线状态(1:在线,0:离线)")
    private Integer isOnline;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUserId;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "终端sn")
    private String deviceSn;

    @ApiModelProperty(value = "终端状态")
    private String deviceStatus;

    @ApiModelProperty(value = "低电量状态")
    private Byte lowBatteryState;

    @ApiModelProperty(value = "低电量状态名称")
    private String lowBatteryStateName;

    @ApiModelProperty(value = "呼叫状态")
    private Byte callState;

    @ApiModelProperty(value = "呼叫状态名称")
    private String callStateName;

    @ApiModelProperty(value = "防拆状态")
    private Byte splitState;

    @ApiModelProperty(value = "防拆状态名称")
    private String splitStateName;

    @ApiModelProperty(value = "红外状态")
    private Byte infraredState;

    @ApiModelProperty(value = "红外状态名称")
    private String infraredStateName;

    @ApiModelProperty(value = "用户名称")
    private String showName;

    @ApiModelProperty(value = "布防状态")
    private Byte defenceState;

    @ApiModelProperty(value = "布防状态名称")
    private String defenceStateName;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "实时位置")
    private UserLocationVO userLocationVO;

    public void setInfraredState(Byte infraredState) {
        this.infraredState = infraredState;
        if (ObjectUtils.isNotEmpty(infraredState)) {
            infraredStateName = infraredState.equals(WhetherConstant._YES) ? "正常" : "报警";
        }
    }

    public void setSplitState(Byte splitState) {
        this.splitState = splitState;
        if (ObjectUtils.isNotEmpty(splitState)) {
            splitStateName = splitState.equals(WhetherConstant._YES) ? "正常" : "报警";
        }
    }

    public void setCallState(Byte callState) {
        this.callState = callState;
        if (ObjectUtils.isNotEmpty(callState)) {
            callStateName = callState.equals(WhetherConstant._YES) ? "正常" : "报警";
        }
    }

    public void setLowBatteryState(Byte lowBatteryState) {
        this.lowBatteryState = lowBatteryState;
        if (ObjectUtils.isNotEmpty(lowBatteryState)) {
            lowBatteryStateName = lowBatteryState.equals(WhetherConstant._YES) ? "正常" : "报警";
        }
    }

    public void setDefenceState(Byte defenceState) {
        this.defenceState = defenceState;
        if (ObjectUtils.isNotEmpty(defenceState)) {
            defenceStateName = defenceState.equals(WhetherConstant._YES) ? "布防" : "撤防";
        }
    }
}
