package com.rutong.medical.admin.service.device.impl;

import com.rutong.medical.admin.entity.device.DeviceTypeBusiness;
import com.rutong.medical.admin.mapper.device.DeviceTypeBusinessMapper;
import com.rutong.medical.admin.service.device.DeviceTypeBusinessService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * 设备分类业务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class DeviceTypeBusinessServiceImpl extends ServiceImpl<DeviceTypeBusinessMapper, DeviceTypeBusiness> implements DeviceTypeBusinessService {

    @Autowired
    private DeviceTypeBusinessMapper deviceTypeBusinessMapper;

}
