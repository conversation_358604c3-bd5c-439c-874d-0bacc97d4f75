package com.rutong.medical.admin.entity.alarm;

import com.rutong.medical.admin.vo.alarm.AlarmDetailMonitorImgVO;
import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * @ClassName AlarmDetailMonitorImg
 * @Description
 * <AUTHOR>
 * @Date 2025/7/29 11:44
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Data
@TableName(value = "sm_alarm_detail_monitor_img")
public class AlarmDetailMonitorImg {

    /** 报警监控照片表ID */
    @TableId(value = "id")
    private Long id;

    /** 报警记录表ID */
    private Long alarmDetailId;

    /** 视频监控表ID */
    private Long deviceMonitorId;

    /** 照片路径 */
    private String imgPath;

    /** 创建时间 */
    private Date createTime;


    @Mapper
    public interface AlarmDetailMonitorImgModelMapper extends BaseModelMapper<AlarmDetailMonitorImgVO, AlarmDetailMonitorImg> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        AlarmDetailMonitorImg toModel(AlarmDetailMonitorImgVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        AlarmDetailMonitorImgVO fromModel(AlarmDetailMonitorImg entity);
    }

    public static final AlarmDetailMonitorImgModelMapper INSTANCE = Mappers.getMapper(AlarmDetailMonitorImgModelMapper.class);
}
