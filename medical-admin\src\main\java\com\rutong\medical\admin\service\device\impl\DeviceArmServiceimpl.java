package com.rutong.medical.admin.service.device.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.dto.device.DeviceArmDTO;
import com.rutong.medical.admin.entity.defence.SmInvadeDefence;
import com.rutong.medical.admin.entity.device.DeviceArm;
import com.rutong.medical.admin.mapper.defence.SmInvadeDefenceMapper;
import com.rutong.medical.admin.mapper.device.DeviceArmMapper;
import com.rutong.medical.admin.service.device.DeviceArmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025-08-01
 */
@Service
public class DeviceArmServiceimpl extends ServiceImpl<DeviceArmMapper, DeviceArm> implements DeviceArmService {

    @Autowired
    private DeviceArmMapper deviceArmMapper;

    @Autowired
    private SmInvadeDefenceMapper smInvadeDefenceMapper;


    @Override
    public boolean isActive(DeviceArmDTO deviceArmDTO) {

        // 设备 更新时间
        LocalDateTime deviceUpdateTime = LocalDateTime.now();

        // 当前设备 布防信息
        DeviceArm deviceArm = deviceArmMapper.selectById(deviceArmDTO.getId());

        // 获取防区id
        Long invadeDefenceId = deviceArm.getInvadeDefenceId();

        // 当前设备关联的防区
        SmInvadeDefence smInvadeDefence = smInvadeDefenceMapper.selectById(invadeDefenceId);

        // 防区 更新时间
        LocalDateTime defenceUpdateTime = smInvadeDefence.getUpdateTime();

        // 更新时间
        if (defenceUpdateTime.isAfter(deviceUpdateTime)) {
            deviceArmDTO.setDefenceState(smInvadeDefence.getDefenceState());
        }

        return deviceArmMapper.updateState(deviceArmDTO);
    }

    @Override
    public boolean autoArm(DeviceArmDTO deviceArmDTO) {

        // 设备 更新时间
        LocalDateTime deviceUpdateTime = LocalDateTime.now();

        // 当前设备 布防信息
        DeviceArm deviceArm = deviceArmMapper.selectById(deviceArmDTO.getId());

        // 获取防区id
        Long invadeDefenceId = deviceArm.getInvadeDefenceId();

        // 当前设备关联的防区
        SmInvadeDefence smInvadeDefence = smInvadeDefenceMapper.selectById(invadeDefenceId);

        // 防区 更新时间
        LocalDateTime defenceUpdateTime = smInvadeDefence.getUpdateTime();

        // 更新自动布防时间
        if (defenceUpdateTime.isAfter(deviceUpdateTime)) {
            deviceArmDTO.setStartTime(smInvadeDefence.getStartTime());
            deviceArmDTO.setEndTime(smInvadeDefence.getEndTime());
        }

        return deviceArmMapper.autoArm(deviceArmDTO);
    }
}
