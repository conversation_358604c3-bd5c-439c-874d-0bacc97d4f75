package com.rutong.medical.admin.service.alarm;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rutong.medical.admin.dto.alarm.AlarmDetailPageQueryDTO;
import com.rutong.medical.admin.dto.alarm.AlarmDisposeDTO;
import com.rutong.medical.admin.entity.alarm.AlarmDetail;
import com.rutong.medical.admin.vo.alarm.AlarmDetailTDVO;
import com.rutong.medical.admin.vo.alarm.AlarmDetailVO;
import com.soft.common.core.object.MyPageData;

import java.util.List;
import java.util.Map;

/**
 * @ClassName AlarmDetailService
 * @Description
 * <AUTHOR>
 * @Date 2025/7/15 10:23
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
public interface AlarmDetailService extends IService<AlarmDetail> {


    /**
     * 获取设备状态
     *
     * @param deviceSns
     * @return
     */
    Map<String, String> getDeviceStatus(List<String> deviceSns);

    /**
     * 获取所有的报警记录
     *
     * @return
     */
    List<AlarmDetailTDVO> getAlarmDetailList(String businessCode);

    /**
     * 分页获取报警记录
     *
     * @param alarmDetailPageQueryDTO
     * @return
     */
    MyPageData<AlarmDetailVO> page(AlarmDetailPageQueryDTO alarmDetailPageQueryDTO);

    /**
     * 处理报警
     *
     * @param id
     * @return
     */
    boolean dispose(AlarmDisposeDTO alarmDisposeDTO);

    /**
     * 报警记录详情
     *
     * @param id
     * @return
     */
    AlarmDetailVO detail(Long id);
}
