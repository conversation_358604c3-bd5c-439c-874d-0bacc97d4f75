package com.rutong.medical.admin.service.device;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rutong.medical.admin.dto.device.DeviceArmDTO;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.entity.device.DeviceArm;

/**
 * <AUTHOR>
 * @Date 2025-08-01
 */
public interface DeviceArmService extends IService<DeviceArm> {
    boolean isActive(DeviceArmDTO deviceArmDTO);

    boolean autoArm(DeviceArmDTO deviceArmDTO);
}
