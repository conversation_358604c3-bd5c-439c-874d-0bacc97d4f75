package com.rutong.medical.admin.mapper.alarm;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.rutong.medical.admin.vo.alarm.AlarmDetailTDVO;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @ClassName AlarmDetailTDMapper
 * @Description
 * <AUTHOR>
 * @Date 2025/7/22 16:36
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@DS("tdengine")
public interface AlarmDetailTDMapper {

    /**
     * 获取所有的报警记录
     *
     * @return
     */
    List<AlarmDetailTDVO> selectAlarmDetailList(@Param("businessCode") String businessCode,@Param("startTime") Date startTime,
                                                @Param("endTime") Date endTime);
}
