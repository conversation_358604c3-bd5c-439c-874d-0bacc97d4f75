package com.rutong.medical.admin.vo.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * DeviceTypeBusinessVO视图对象
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@ApiModel("DeviceTypeBusinessVO视图对象")
@Data
public class DeviceTypeBusinessVO {

    @ApiModelProperty(value = "设备分类业务表ID")
    private Long id;

    @ApiModelProperty(value = "设备分类表编号")
    private String typeCode;

    @ApiModelProperty(value = "业务编号")
    private String businessCode;


}
