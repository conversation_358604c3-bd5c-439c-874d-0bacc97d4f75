package com.rutong.medical.admin.service.system;

import java.util.List;

import com.rutong.medical.admin.dto.system.SpaceQueryDTO;
import com.rutong.medical.admin.dto.system.SpaceSaveDTO;
import com.rutong.medical.admin.dto.system.SpaceUpdateDTO;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.vo.system.SpaceQueryVO;
import com.rutong.medical.admin.vo.system.SpaceVO;
import com.soft.common.core.base.service.IBaseService;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;

import cn.hutool.core.lang.tree.Tree;

/**
 * 空间Service接口
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
public interface SpaceService extends IBaseService<Space, Long> {

    /**
     * 空间列表查询
     *
     * @param spaceQueryDTO 查询条件
     * @return
     */
    MyPageData<SpaceVO> pageList(SpaceQueryDTO spaceQueryDTO);

    /**
     * 新建空间
     *
     * @param spaceSaveDTO
     * @return
     */
    int save(SpaceSaveDTO spaceSaveDTO);

    /**
     * 更新空间
     *
     * @param spaceUpdateDTO
     * @return
     */
    int update(SpaceUpdateDTO spaceUpdateDTO);

    /**
     * 删除空间
     *
     * @param spaceId
     * @return
     */
    int delete(Long spaceId);

    /**
     * 查询空间树形列表结构
     *
     * @param deep
     * @param displayCode
     * @return
     */
    List<Tree<String>> tree(boolean isAll, Integer deep, boolean displayCode);

    /**
     * 生成空间二维码
     *
     * @param id
     * @return
     */
    String generatorQrcode(Long id) throws Exception;

    /**
     * 导出二维码文件
     */
    void exportQrcode(SpaceQueryDTO spaceQueryDTO);

    /**
     * <AUTHOR>
     * @Description 根据full_name模糊查询
     * @Date 下午5:46 2024/9/10
     * @Param [spaceQueryDTO]
     * @return java.util.List<com.soft.sub.vo.system.SpaceVO>
     **/
    List<SpaceVO> listSpace(SpaceQueryDTO spaceQueryDTO);

    /**
     * @return com.soft.sub.vo.system.SpaceVO
     * <AUTHOR>
     * @Description 根据id查询空间信息
     * @Date 下午1:32 2024/10/15
     * @Param [id]
     **/
    SpaceVO getSpaceById(Long id);

    /**
     * 系统树，不显示四级
     * 
     * @return
     */
    ResponseResult<List<Tree<String>>> listTree(Long equipmentTypeId);

    /**
     * 获取空间信息
     * 
     * @param spaceQueryVO
     * @return
     */
    List<SpaceVO> getSpace(SpaceQueryVO spaceQueryVO);

    /**
     * 根据id查询空间信息
     *
     * @param id
     * @return
     */
    Space getSpaceCacheById(Long id);

    /**
     * 查询空间树形列表结构根据设备分类
     *
     * @param deviceTypeId
     * @return
     */
    List<Tree<String>> spaceTreeByTypeId(Long deviceTypeId);
}
