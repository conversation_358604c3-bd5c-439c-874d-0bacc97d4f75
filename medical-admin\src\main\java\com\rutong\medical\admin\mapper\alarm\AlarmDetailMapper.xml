<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.alarm.AlarmDetailMapper">
    <resultMap type="com.rutong.medical.admin.entity.alarm.AlarmDetail" id="AlarmDetailResult">
        <result property="id" column="id" />
        <result property="deviceBaseStationSn" column="device_base_station_sn" />
        <result property="userId" column="user_id" />
        <result property="deviceSn" column="device_sn" />
        <result property="businessCode" column="business_code" />
        <result property="userName" column="user_name" />
        <result property="deviceTypeCode" column="device_type_code" />
        <result property="alarmType" column="alarm_type" />
        <result property="disposeState" column="dispose_state" />
        <result property="spaceId" column="space_id" />
        <result property="alarmTime" column="alarm_time" />
        <result property="disposeTime" column="dispose_time" />
    </resultMap>

    <sql id="selectAlarmDetailVo">
        select id, device_base_station_id, user_id, device_id, device_sn, business_code, user_name, alarm_awy, alarm_type, dispose_state, space_id, report_time, dispose_time from alarm_detail
    </sql>
    <select id="pageList" resultType="com.rutong.medical.admin.vo.alarm.AlarmDetailVO" parameterType="com.rutong.medical.admin.dto.alarm.AlarmDetailPageQueryDTO">
        select t.user_name,t.id,x.device_name,
        (select b.dept_name from common_sys_dept_user a,common_sys_dept b where a.dept_id=b.dept_id and
        a.user_id=t.user_id) as dept_name,device_type_code,
        (select a.full_name from sp_space a where a.id=t.space_id) as space_full_name,
        t.device_sn,alarm_type,t.space_id,t.dispose_state,t.alarm_time,t.dispose_time
        from sm_alarm_detail t left join sm_device x on t.device_sn=x.device_sn
        <where>
        <if test="deviceCondition != null and deviceCondition != ''">
            and (device_sn=#{deviceCondition} or device_sn = (select device_sn from sm_device where device_name like
            concat('%',#{deviceCondition},'%')))
        </if>
        <if test="spaceId != null">
            and space_id = #{spaceId}
        </if>
        <if test="disposeState != null">
            and dispose_state = #{disposeState}
        </if>
        <if test="deviceTypeCode != null and deviceTypeCode != ''">
            and device_type_code = #{deviceTypeCode}
        </if>
        <if test="businessCode != null and businessCode != ''">
            and FIND_IN_SET(#{businessCode},t.business_code)
        </if>
        <if test="alarmDateStart != null and alarmDateStart != ''">
            and alarm_time >= concat(#{alarmDateStart}, '00:00:00')
        </if>
        <if test="alarmDateEnd != null and alarmDateEnd != ''">
            and alarm_time &lt;= concat(#{alarmDateEnd}, '23:59:59')
        </if>
        </where>
    </select>

</mapper>