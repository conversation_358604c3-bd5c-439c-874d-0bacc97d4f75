package com.rutong.medical.admin.service.location.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rutong.medical.admin.constant.DeviceTypeEnum;
import com.rutong.medical.admin.dto.location.UserLocationDTO;
import com.rutong.medical.admin.entity.alarm.AlarmDetail;
import com.rutong.medical.admin.entity.monitor.DeviceMonitor;
import com.rutong.medical.admin.entity.station.DeviceBaseStation;
import com.rutong.medical.admin.entity.station.DeviceBaseStationMonitor;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.mapper.location.UserTrackTDMapper;
import com.rutong.medical.admin.mapper.monitor.DeviceMonitorMapper;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationMapper;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationMonitorMapper;
import com.rutong.medical.admin.mapper.system.SpaceMapper;
import com.rutong.medical.admin.service.location.UserTrackService;
import com.rutong.medical.admin.service.station.DeviceBaseStationMonitorService;
import com.rutong.medical.admin.service.system.SpaceService;
import com.rutong.medical.admin.vo.location.UserLocationVO;
import com.soft.common.core.util.MyModelUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName UserTrackServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/25 10:41
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Service
@AllArgsConstructor
public class UserTrackServiceImpl implements UserTrackService {

    private UserTrackTDMapper userTrackTDMapper;
    private DeviceBaseStationMonitorMapper deviceBaseStationMonitorMapper;
    private DeviceMonitorMapper deviceMonitorMapper;
    private DeviceBaseStationMapper deviceBaseStationMapper;
    private SpaceMapper spaceMapper;

    @Override
    public Map<Space, List<UserLocationVO>> getUserTrack(UserLocationDTO userLocationDTO) {
        userLocationDTO.setDeviceTypeCode(DeviceTypeEnum.CARD_LORA.getCode());
        return userTrackTDMapper.selectUserTrack(userLocationDTO).stream().map(userLocation -> {
            DeviceBaseStation deviceBaseStation = deviceBaseStationMapper.selectOne(Wrappers.lambdaQuery(DeviceBaseStation.class)
                    .eq(DeviceBaseStation::getDeviceBaseStationCode, userLocation.getNewLocatorSn()));
            if (ObjectUtils.isEmpty(deviceBaseStation)) {
                return null;
            }
            List<DeviceBaseStationMonitor> deviceBaseStationMonitorList = deviceBaseStationMonitorMapper
                    .selectList(Wrappers.lambdaQuery(DeviceBaseStationMonitor.class)
                            .eq(DeviceBaseStationMonitor::getDeviceBaseStationId, deviceBaseStation.getId()));

            List<DeviceMonitor> deviceMonitorList = new ArrayList<>();
            for (DeviceBaseStationMonitor deviceBaseStationMonitor : deviceBaseStationMonitorList) {
                DeviceMonitor deviceMonitor = deviceMonitorMapper.selectOne(Wrappers.lambdaQuery(DeviceMonitor.class).eq(DeviceMonitor::getId,
                        deviceBaseStationMonitor.getDeviceMonitorId()));
                deviceMonitorList.add(deviceMonitor);
            }
            UserLocationVO userLocationVO = MyModelUtil.copyTo(userLocation, UserLocationVO.class);
            userLocationVO.setDeviceMonitorList(deviceMonitorList);
            return userLocationVO;
        }).collect(Collectors.groupingBy(
                userLocationVO -> spaceMapper.selectById(userLocationVO.getFloorId()),
                LinkedHashMap::new,
                Collectors.toList()
        ));
    }
}
