package com.rutong.medical.admin.dto.location;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * @ClassName UserLocationVO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/22 15:50
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@ApiModel("人员定位")
@Data
public class UserLocationDTO {

    @ApiModelProperty(value = "姓名、工号、设备SN")
    private String userCondition;

    @ApiModelProperty(value = "开始时间")
    @NonNull
    private String createTimeStart;

    @ApiModelProperty(value = "结束时间")
    @NonNull
    private String createTimeEnd;

    @ApiModelProperty(value = "设备类型")
    private Integer deviceTypeCode;


}
