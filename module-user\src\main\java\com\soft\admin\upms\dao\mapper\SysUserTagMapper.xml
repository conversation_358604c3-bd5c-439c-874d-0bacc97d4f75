<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysUserTagMapper">
    <resultMap type="com.soft.admin.upms.model.SysUserTag" id="SysUserTagResult">
        <result property="userId" column="user_id" />
        <result property="tagId" column="tag_id" />
    </resultMap>

    <sql id="selectSysUserTagVo">
        select user_id, tag_id from common_sys_user_tag
    </sql>
    <insert id="insertBatch">
        insert into common_sys_user_tag
        values
            <foreach collection="list" item="item" separator=",">
                (#{item.userId}, #{item.tagId})
            </foreach>
    </insert>

    <select id="getTagByUserId" resultType="com.soft.admin.upms.model.SysTag">
        select c.code
        from common_sys_user_tag b,
             common_sys_tag c
        where b.tag_id = c.id
          and b.user_id = #{userId};
    </select>

</mapper>