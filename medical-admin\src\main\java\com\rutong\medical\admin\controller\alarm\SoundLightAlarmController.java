package com.rutong.medical.admin.controller.alarm;


import com.rutong.medical.admin.dto.alarm.SoundLightAlarmDTO;
import com.rutong.medical.admin.service.alarm.SoundLightAlarmService;
import com.rutong.medical.admin.vo.alarm.SoundLightAlarmVO;
import com.soft.common.core.object.ResponseResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2025-07-15
 * 报警设置表Controller
 */
@Api(tags = "报警设置管理")
@RestController
@RequestMapping("/alarm/config")
public class SoundLightAlarmController {

    @Autowired
    private SoundLightAlarmService soundLightAlarmService;

    /**
     * 更新单个报警配置
     *
     * @param soundLightAlarmDTO 报警配置DTO
     * @return 更新结果
     */
    @ApiOperation("更新报警配置")
    @PostMapping("/update")
    public ResponseResult<Boolean> updateAlarmConfig(@RequestBody SoundLightAlarmDTO soundLightAlarmDTO) {
        return ResponseResult.success(soundLightAlarmService.updateAlarmConfig(soundLightAlarmDTO));
    }

    /**
     * 获取所有报警配置
     *
     * @return 所有报警配置列表
     */
    @ApiOperation("获取指定报警配置")
    @GetMapping("/list")
    public ResponseResult<SoundLightAlarmVO> getAllAlarmConfigs(String configCode) {
        return ResponseResult.success(soundLightAlarmService.getAlarmConfig(configCode));
    }


}
