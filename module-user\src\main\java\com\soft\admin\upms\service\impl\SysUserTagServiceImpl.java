package com.soft.admin.upms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.admin.upms.dao.SysUserTagMapper;
import com.soft.admin.upms.model.SysTag;
import com.soft.admin.upms.model.SysUserTag;
import com.soft.admin.upms.service.SysUserTagService;
import com.soft.common.core.constant.RedisKeyConstant;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户标签关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Service
public class SysUserTagServiceImpl extends ServiceImpl<SysUserTagMapper, SysUserTag> implements SysUserTagService {

    @Resource
    private SysUserTagMapper sysUserTagMapper;

    @Override
    @Cacheable(value = RedisKeyConstant.SYSTEM_BASEINFO_USER_TAG, key = "#userId")
    public SysTag getTagByUserId(Long userId) {
        return sysUserTagMapper.getTagByUserId(userId);
    }
}
