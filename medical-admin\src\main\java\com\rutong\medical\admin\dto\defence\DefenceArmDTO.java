package com.rutong.medical.admin.dto.defence;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 布防/撤防/自动布防 DTO
 * <AUTHOR>
 * @Date 2025-07-28
 */
@Data
@ApiModel(value = "布防/撤防/自动布防请求参数")
public class DefenceArmDTO {

    /**
     * 防区ID列表（支持批量操作）
     */
    @ApiModelProperty(value = "防区ID列表", required = true, example = "[1, 2, 3]")
    @NotEmpty(message = "防区ID列表不能为空")
    private List<Long> defenceIds;

    /**
     * 操作类型：1-手动布防/撤防，2-自动布防
     */
    @ApiModelProperty(value = "操作类型：1-手动布防/撤防，2-自动布防", required = true, example = "1")
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;

    /**
     * 布防状态（仅当operationType=1时使用）
     * 0-撤防，1-布防
     */
    @ApiModelProperty(value = "布防状态：0-撤防，1-布防（仅手动操作时使用）", example = "1")
    private Integer defenceState;

    /**
     * 自动布防开始时间（仅当operationType=2时使用）
     */
    @JsonFormat(pattern="HH:mm:ss")
    @ApiModelProperty(value = "自动布防开始时间（仅自动布防时使用）", example = "2025-07-28T08:00:00")
    private LocalTime startTime;

    /**
     * 自动布防结束时间（仅当operationType=2时使用）
     */
    @JsonFormat(pattern="HH:mm:ss")
    @ApiModelProperty(value = "自动布防结束时间（仅自动布防时使用）", example = "2025-07-28T18:00:00")
    private LocalTime endTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUserId;

    @ApiModelProperty(value = "更新时间")
    private LocalDate updateTime;
}
