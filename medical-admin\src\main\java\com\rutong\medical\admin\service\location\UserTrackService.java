package com.rutong.medical.admin.service.location;

import com.rutong.medical.admin.dto.location.UserLocationDTO;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.vo.location.UserLocationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @ClassName UserTrackService
 * @Description
 * <AUTHOR>
 * @Date 2025/7/25 10:41
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
public interface UserTrackService {

    /**
     * 获取用户移动轨迹
     * @param userLocationDTO
     * @return
     */
    Map<Space, List<UserLocationVO>> getUserTrack(UserLocationDTO userLocationDTO);
}
