package com.rutong.medical.admin.service.defence.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.entity.defence.SmDeviceLayoutDefence;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.mapper.defence.SmDeviceLayoutDefenceMapper;
import com.rutong.medical.admin.mapper.device.DeviceMapper;
import com.rutong.medical.admin.service.defence.SmDeviceLayoutDefenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-19
 */
@Service
public class SmDeviceLayoutDefenceServiceImpl extends ServiceImpl<SmDeviceLayoutDefenceMapper, SmDeviceLayoutDefence> implements SmDeviceLayoutDefenceService {

    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public List<Device> getAllDevice() {
        List<Device> devices = deviceMapper.selectDevice();
        return devices;
    }
}
