package com.rutong.medical.admin.listener;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.rutong.medical.admin.constant.DeviceTypeEnum;
import com.rutong.medical.admin.dto.station.BaseStationDataDTO;
import com.rutong.medical.admin.service.location.UserLocationService;
import com.soft.common.mqtt.MQTTTopicMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName InfraredMonitorListener
 * @Description
 * <AUTHOR>
 * @Date 2025/7/29 15:39
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Component
@Slf4j
public class InfraredMonitorListener implements MQTTTopicMessageListener {

    // 监听的主题名称
    private static final String LISTENER_TOPIC_NAME = "/baseStation/location/+/"+ DeviceTypeEnum.INFRARED_MONITOR_433.getCode()+"/data";

    @Autowired
    private UserLocationService userLocationService;

    @Override
    public String getTopic() {
        return LISTENER_TOPIC_NAME;
    }

    @Override
    public Integer getQos() {
        return 0;
    }

    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) {
        try {
            String payload = new String(mqttMessage.getPayload());
            System.out.println(payload);
            processPayload(payload);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }


    public void processPayload(String payload) {
        if (StringUtils.isBlank(payload)) {
            return;
        }
        try {
            JSON json = JSONUtil.parse(payload);
            BaseStationDataDTO baseStationData = json.toBean(BaseStationDataDTO.class);
            userLocationService.infraredMonitorSave(baseStationData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
