package com.rutong.medical.admin.controller.monitor;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.rutong.medical.admin.dto.monitor.DeviceMonitorQueryDTO;
import com.rutong.medical.admin.dto.monitor.DeviceMonitorSaveDTO;
import com.rutong.medical.admin.dto.monitor.EquipmentControlParamDTO;
import com.rutong.medical.admin.service.monitor.DeviceMonitorService;
import com.rutong.medical.admin.vo.monitor.DeviceMonitorVO;
import com.rutong.medical.admin.vo.monitor.EquipmentImportCheckVO;
import com.soft.common.core.annotation.NoAuthInterface;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 视频监控控制器类
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@Api(tags = "视频监控")
@RestController
@RequestMapping("/device/monitor/")
public class DeviceMonitorController {

    @Autowired
    private DeviceMonitorService deviceMonitorService;

    @ApiOperation(value = "视频监控列表")
    @GetMapping("list")
    public ResponseResult<List<DeviceMonitorVO>> list(DeviceMonitorQueryDTO deviceMonitorQueryDTO) {
        return ResponseResult.success(deviceMonitorService.listMonitor(deviceMonitorQueryDTO));
    }

    @ApiOperation(value = "视频监控分页查询")
    @GetMapping("page")
    public ResponseResult<MyPageData<DeviceMonitorVO>> page(DeviceMonitorQueryDTO deviceMonitorQueryDTO) {
        return ResponseResult.success(deviceMonitorService.page(deviceMonitorQueryDTO));
    }

    @ApiOperation(value = "删除视频监控")
    @PostMapping("delete/{id}")
    public ResponseResult<Void> delete(@PathVariable(value = "id", required = true) Long id) {
        deviceMonitorService.delete(id);
        return ResponseResult.success();
    }

    @ApiOperation(value = "新增或者修改视频监控")
    @PostMapping("saveOrUpdate")
    public ResponseResult<Void> saveOrUpdate(@RequestBody DeviceMonitorSaveDTO deviceMonitorSaveDTO) {
        deviceMonitorService.saveOrUpdate(deviceMonitorSaveDTO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "视频监控详情接口")
    @GetMapping("detail/{id}")
    public ResponseResult<DeviceMonitorVO> detail(@PathVariable("id") Long id) {
        return ResponseResult.success(deviceMonitorService.detail(id));
    }

    @NoAuthInterface
    @ApiOperation("下载模板文件")
    @GetMapping("/export/template")
    public void downExcel() {
        deviceMonitorService.exportTemplate();
    }

    @ApiOperation(value = "获取视频厂家")
    @GetMapping("getFactory")
    @NoAuthInterface
    public ResponseResult<Map<String, String>> getFactory() {
        return ResponseResult.success(deviceMonitorService.getFactory());
    }

    @ApiOperation(value = "导入数据")
    @PostMapping("/import")
    public ResponseResult<Void> importExcel(@RequestBody EquipmentImportCheckVO equipmentImportCheckVO)
        throws IOException {
        deviceMonitorService.importExcel(equipmentImportCheckVO);
        return ResponseResult.success();
    }

    @ApiOperation(value = "通用监控设备转码后的flv视频播放地址")
    @PostMapping("/previewFlvUrl")
    public ResponseResult<String> previewFlv(@RequestBody EquipmentControlParamDTO methodParam) {
        return ResponseResult.success(deviceMonitorService.playFlv(methodParam));
    }

    @ApiOperation("导入设备文件")
    @PostMapping("/check")
    public ResponseResult<EquipmentImportCheckVO> importCheck(MultipartFile file) throws IOException {
        EquipmentImportCheckVO equipmentImportCheckVO = deviceMonitorService.importCheck(file);
        return ResponseResult.success(equipmentImportCheckVO);
    }

    @ApiOperation(value = "根据基站获取视频地址")
    @GetMapping("/playFlvByBaseStation")
    @NoAuthInterface
    public ResponseResult<List<String>> playFlvByBaseStation(@RequestParam(required = true) String baseStationSn) {
        return ResponseResult.success(deviceMonitorService.playFlvByBaseStation(baseStationSn));
    }
}
