package com.rutong.medical.admin.service.system.impl;

import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.mapper.device.DeviceMapper;
import com.soft.common.core.constant.RedisKeyConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
//import org.eclipse.jetty.util.ajax.JSON;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageMar;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freewayso.image.combiner.ImageCombiner;
import com.github.pagehelper.PageHelper;
import com.rutong.medical.admin.constant.SpaceTypeEnum;
import com.rutong.medical.admin.dto.system.SpaceQueryDTO;
import com.rutong.medical.admin.dto.system.SpaceSaveDTO;
import com.rutong.medical.admin.dto.system.SpaceUpdateDTO;
import com.rutong.medical.admin.entity.station.DeviceBaseStation;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.mapper.station.DeviceBaseStationMapper;
import com.rutong.medical.admin.mapper.system.SpaceMapper;
import com.rutong.medical.admin.service.system.SpaceService;
import com.rutong.medical.admin.vo.system.SpaceQueryVO;
import com.rutong.medical.admin.vo.system.SpaceVO;
import com.soft.admin.upms.dao.SysUserMapper;
import com.soft.admin.upms.model.SysUser;
import com.soft.admin.upms.service.SysConfigService;
import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.common.core.base.service.BaseService;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.constant.SysConfigConstants;
import com.soft.common.core.exception.MyRuntimeException;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import com.soft.common.core.object.TokenData;
import com.soft.common.core.util.ImageCombinerUtils;
import com.soft.common.core.util.MyModelUtil;
import com.soft.common.core.util.MyPageUtil;
import com.soft.common.sequence.wrapper.IdGeneratorWrapper;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 空间Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Service
@Slf4j
public class SpaceServiceImpl extends BaseService<Space, Long> implements SpaceService {

    @Resource
    private SpaceMapper spaceMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private DeviceBaseStationMapper deviceBaseStationMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private IdGeneratorWrapper idGeneratorWrapper;
    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private SysConfigService sysConfigService;

    @Autowired
    private HttpServletResponse response;

    @Override
    protected BaseDaoMapper<Space> mapper() {
        return spaceMapper;
    }

    @Override
    public MyPageData<SpaceVO> pageList(SpaceQueryDTO spaceQueryDTO) {
        List<Space> spaces = getPageList(spaceQueryDTO);
        MyPageData<SpaceVO> pageData = MyPageUtil.makeResponseData(spaces, Space.INSTANCE);

        // 查询空间对应的项目名称和 创建人名称
        List<SpaceVO> spaceVOS = pageData.getDataList();
        for (SpaceVO spaceVO : spaceVOS) {
            Long createUserId = spaceVO.getCreateUserId();
            Optional.ofNullable(createUserId).ifPresent(userId -> {
                SysUser sysUser = sysUserMapper.selectById(userId);
                Optional.ofNullable(sysUser).ifPresent(user -> spaceVO.setCreateUsername(sysUser.getShowName()));
            });
        }

        return pageData;
    }

    /**
     * 分页查询
     *
     * @param spaceQueryDTO
     * @return
     */
    public List<Space> getPageList(SpaceQueryDTO spaceQueryDTO) {
        String name = spaceQueryDTO.getName();
        String path = spaceQueryDTO.getPath();
        SpaceTypeEnum type = spaceQueryDTO.getType();
        PageHelper.startPage(spaceQueryDTO.getPageNum(), spaceQueryDTO.getPageSize());
        LambdaQueryWrapper<Space> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Space::getType, type).like(StringUtils.isNotBlank(name), Space::getName, name)
            .eq(spaceQueryDTO.getSpaceId() != null, Space::getParentId, spaceQueryDTO.getSpaceId())
            .likeRight(StringUtils.isNotBlank(path), Space::getPath, path + "/").orderByAsc(Space::getSort);
        return spaceMapper.selectList(queryWrapper);
    }

    /**
     * 修改空间
     *
     * @param spaceUpdateDTO
     * @return
     */
    @Override
    @Transactional
    public int update(SpaceUpdateDTO spaceUpdateDTO) {
        Long id = spaceUpdateDTO.getId();
        String code = spaceUpdateDTO.getCode();
        String name = spaceUpdateDTO.getName();
        Long parentId = spaceUpdateDTO.getParentId();

        Space space = spaceMapper.selectById(id);
        checkIsNull(space, "空间不存在或已删除！");

        // 判断编号不能相同
        Long count = spaceMapper.selectCount(Wrappers.lambdaQuery(Space.class).eq(Space::getCode, code)
            .eq(Space::getParentId, parentId).ne(Space::getId, id));
        if (count > 0) {
            throw new MyRuntimeException("编号已存在！");
        }

        // 修改子空间使用
        AtomicBoolean updateSon = new AtomicBoolean(false);
        String oldPath = space.getPath();
        String oldFullName = space.getFullName();

        // 编码没有和其他数据耦合，可以先设置
        space.setCode(code);
        space.setCoordinate(spaceUpdateDTO.getCoordinate());
        space.setModelTwoDimensional(spaceUpdateDTO.getModelTwoDimensional());
        space.setModelThreeDimensional(spaceUpdateDTO.getModelThreeDimensional());

        // 父级空间发生修改时，需要修改耦合的字段
        if (!Objects.equals(parentId, space.getParentId())) {
            Space parentSpace = spaceMapper.selectById(parentId);
            Optional.ofNullable(parentSpace).ifPresent(ps -> {
                space.setParentId(parentId);
                space.setPath(ps.getPath() + "/" + id);
                space.setFullName(ps.getFullName() + "/" + name);

                // 标识需要修改子空间
                updateSon.set(true);
            });
        }

        // 名称发生修改时，需要修改耦合的全名称字段
        if (!Objects.equals(name, space.getName())) {
            space.setName(name);
            if (Objects.equals("AREA", space.getType())) {
                space.setFullName(name);
            } else {
                int index = oldFullName.lastIndexOf("/");
                String substring = oldFullName.substring(0, index);
                String fullName = substring + "/" + name;
                space.setFullName(fullName);
            }

            // 标识需要修改子空间
            updateSon.set(true);
        }

        // 修改子空间中耦合的字段
        if (updateSon.get()) {
            LambdaQueryWrapper<Space> queryWrapper = Wrappers.lambdaQuery(Space.class);
            queryWrapper.likeRight(Space::getPath, oldPath + "/");
            List<Space> spaces = spaceMapper.selectList(queryWrapper);
            for (Space sonSpace : spaces) {
                String path = sonSpace.getPath();
                path = path.replace(oldPath, space.getPath());
                sonSpace.setPath(path);
                String fullName = sonSpace.getFullName();
                fullName = fullName.replace(oldFullName, space.getFullName());
                sonSpace.setFullName(fullName);
                spaceMapper.updateById(sonSpace);
            }
        }
        space.setSort(spaceUpdateDTO.getSort());
        space.setCommonArea(spaceUpdateDTO.getCommonArea());
        int updateCount = spaceMapper.updateById(space);
        if (updateCount > 0) {
            SpaceServiceImpl SpaceService = applicationContext.getBean(SpaceServiceImpl.class);
            SpaceService.asyncUpdateSpaceFullName(spaceUpdateDTO);
        }
        return updateCount;
    }

    /**
     * 查询设备信息
     *
     * @param pageNo
     * @param pageSize
     * @param id
     * @return
     */
    private IPage<DeviceBaseStation> getEquipmentIPage(int pageNo, int pageSize, Long id) {
        LambdaQueryWrapper<DeviceBaseStation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DeviceBaseStation::getSpacePath, id);
        queryWrapper.orderByAsc(DeviceBaseStation::getId);
        IPage<DeviceBaseStation> pageData =
            deviceBaseStationMapper.selectPage(new Page<>(pageNo, pageSize), queryWrapper);
        return pageData;
    }

    /**
     * 删除空间
     *
     * @param spaceId
     */
    @Override
    public int delete(Long spaceId) {
        // 查询是否存在下级
        Space space = spaceMapper.selectById(spaceId);
        checkIsNull(space, "空间不存在或已删除！");

        // 判断是否存在下级
        boolean exists = existsSonSpace(space.getPath());
        if (exists) {
            throw new MyRuntimeException("请先删除下级空间！");
        }

        // FIXME 点位删除时，需要判断点位是否存在设备，存在设备则不可删除
        if (Objects.equals(SpaceTypeEnum.POINT.name(), space.getType())) {
            List<DeviceBaseStation> equipment = listEquipmentsBySpaceId(spaceId);
            if (CollectionUtils.isNotEmpty(equipment)) {
                throw new MyRuntimeException("请先删除点位下绑定的设备！");
            }
        }

        // 删除空间
        return spaceMapper.deleteById(spaceId);
    }

    private boolean existsSonSpace(String path) {
        LambdaQueryWrapper<Space> queryWrapper = Wrappers.lambdaQuery(Space.class);
        queryWrapper.like(Space::getPath, path + "/" + "%");
        return spaceMapper.exists(queryWrapper);
    }

    /**
     * 查询指定空间绑定的设备列表
     *
     * @param spaceId
     * @return
     */
    private List<DeviceBaseStation> listEquipmentsBySpaceId(Long spaceId) {
        LambdaQueryWrapper<DeviceBaseStation> queryWrapper = Wrappers.lambdaQuery(DeviceBaseStation.class);
        queryWrapper.eq(spaceId != null, DeviceBaseStation::getSpaceId, spaceId).eq(DeviceBaseStation::getIsDelete,
            GlobalDeletedFlag.NORMAL);
        return deviceBaseStationMapper.selectList(queryWrapper);
    }

    // 空间树
    @Override
    public List<Tree<String>> tree(boolean isAll, Integer deep, boolean displayCode) {
        List<Space> spaces;
        if (isAll) {
            spaces = spaceMapper.queryAll();
        } else {
            LambdaQueryWrapper<Space> queryWrapper = Wrappers.lambdaQuery(Space.class).orderByAsc(Space::getCode);
            spaces = spaceMapper.selectList(queryWrapper);
        }

        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");
        treeNodeConfig.setNameKey("name");
        treeNodeConfig.setChildrenKey("children");
        // 排序字段
        treeNodeConfig.setWeightKey("sort");

        // 最大递归深度
        treeNodeConfig.setDeep(deep);
        // 转换器
        List<Tree<String>> treeList = TreeUtil.build(spaces, null, treeNodeConfig, (treeNode, tree) -> {
            tree.setId(treeNode.getId().toString());
            tree.setParentId(treeNode.getParentId() == null ? null : treeNode.getParentId().toString());
            String fullName = treeNode.getFullName();
            if (displayCode && treeNode.getType().equals("POINT")) {
                tree.setName(treeNode.getCode() + "（" + treeNode.getName() + "）");
                // 去掉最后层级
                fullName = fullName.substring(0, fullName.lastIndexOf("/"));
                // 拼接code
                fullName = fullName + "/" + treeNode.getCode() + "（" + treeNode.getName() + "）";
            } else {
                tree.setName(treeNode.getName());
            }


            // 扩展属性 ...
            tree.putExtra("code", treeNode.getCode());
            tree.putExtra("path", treeNode.getPath());
            tree.putExtra("fullName", fullName);
            tree.putExtra("modelTwoDimensional", treeNode.getModelTwoDimensional());
            tree.putExtra("modelThreeDimensional", treeNode.getModelThreeDimensional());
            // tree.putExtra("deleted", treeNode.getDeleted());
            tree.putExtra("type", treeNode.getType());
            tree.putExtra("sort", treeNode.getSort());
            // tree.putExtra("createUserId", treeNode.getCreateUserId());
            // tree.putExtra("createTime", treeNode.getCreateTime());
            // tree.putExtra("updateUserId", treeNode.getUpdateUserId());
            // tree.putExtra("updateTime", treeNode.getUpdateTime());
        });

        return treeList;
    }
    private List<Tree<String>> removeInvalidThirdLevelNodes(List<Tree<String>> trees,
        Map<Long, Long> objectObjectHashMap) {
        List<Tree<String>> result = new ArrayList<>();
        for (Tree<String> tree : trees) {
            if (tree.getChildren() != null && !tree.getChildren().isEmpty()) {
                List<Tree<String>> validChildren = new ArrayList<>();
                for (Tree<String> child : tree.getChildren()) {
                    if (child.getChildren() == null || child.getChildren().isEmpty()) {
                        // 检查是否为有效节点
                        if (isTrueSpace(Long.parseLong(child.getId()), objectObjectHashMap)) {
                            validChildren.add(child);
                        }
                    } else {
                        // 递归检查子节点
                        List<Tree<String>> validGrandChildren =
                            removeInvalidThirdLevelNodes(child.getChildren(), objectObjectHashMap);
                        if (!validGrandChildren.isEmpty()) {
                            child.setChildren(validGrandChildren);
                            validChildren.add(child);
                        }
                    }
                }
                if (!validChildren.isEmpty()) {
                    tree.setChildren(validChildren);
                    result.add(tree);
                }
            } else {
                // 检查是否为有效节点
                if (isTrueSpace(Long.parseLong(tree.getId()), objectObjectHashMap)) {
                    result.add(tree);
                }
            }
        }
        return result;
    }

    private boolean isTrueSpace(Long spaceId, Map<Long, Long> objectObjectHashMap) {
        return objectObjectHashMap.containsKey(spaceId);
    }

    @Override
    public String generatorQrcode(Long id) throws Exception {
        Space space = spaceMapper.selectById(id);
        if (space == null) {
            throw new RuntimeException("空间不存在或已删除！");
        }
        JSONObject qrContent = new JSONObject();
        String fullName = space.getFullName();

        // 二维码身份：设备
        qrContent.put("identity", "SPACE");
        // 设备种类：运维设备
        qrContent.put("id", space.getId());
        qrContent.put("code", space.getCode());
        qrContent.put("name", space.getName());
        qrContent.put("fullName", fullName);

        // 去掉第一层级
        fullName = fullName.substring(fullName.indexOf("/") + 1);

        if (space.getType().equals(SpaceTypeEnum.POINT.name())) {
            // 去掉最后层级
            fullName = fullName.substring(0, fullName.lastIndexOf("/"));
            // 拼接code
            fullName = fullName + "/" + space.getCode();
        }

        ImageCombiner imageCombiner = null;
        String customerCode = sysConfigService.getValue(SysConfigConstants.CUSTOMERCODE);

        // 区分莫干山儿保
        if (Objects.equals(customerCode, SysConfigConstants.CUSTOMERCODE_MGSEB)) {
            imageCombiner = ImageCombinerUtils.generatorSpaceQrcodeMgseb(qrContent.toJSONString(), fullName);
        } else {
            imageCombiner = ImageCombinerUtils.generatorSpaceQrcode(qrContent.toJSONString(), fullName);
        }

        // 获取图片缓存
        BufferedImage combinedImage = imageCombiner.getCombinedImage();
        // 写出图片缓存到字节数组中
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(combinedImage, "png", baos);
        // 对字节数组进行 base64 编码
        byte[] bytes = baos.toByteArray();
        return Base64Encoder.encode(bytes).trim().replaceAll("\n", "").replaceAll("\r", "");
    }

    @Override
    public void exportQrcode(SpaceQueryDTO spaceQueryDTO) {
        SpaceTypeEnum type = spaceQueryDTO.getType();
        String name = spaceQueryDTO.getName();
        String path = spaceQueryDTO.getPath();

        LambdaQueryWrapper<Space> queryWrapper =
            Wrappers.lambdaQuery(Space.class).like(StringUtils.isNotBlank(name), Space::getName, name)
                .likeRight(StringUtils.isNotBlank(path), Space::getPath, path).orderByAsc(Space::getCode);
        if (type != null) {
            queryWrapper.eq(Space::getType, type.name());
        }

        List<Space> spaces = spaceMapper.selectList(queryWrapper);
        String customerCode = sysConfigService.getValue(SysConfigConstants.CUSTOMERCODE);
        if (CollectionUtils.isNotEmpty(spaces)) {
            // 使用ConcurrentHashMap作为缓存，避免重复生成相同内容的二维码
            Map<String, byte[]> qrCodeCache = new ConcurrentHashMap<>();

            List<byte[]> spaceQrcodeExcelVOS = spaces.parallelStream().map(space -> {
                JSONObject qrContent = new JSONObject();
                String fullName = space.getFullName();
                // 二维码身份：设备
                qrContent.put("identity", "SPACE");
                // 设备种类：运维设备
                qrContent.put("id", space.getId());
                qrContent.put("code", space.getCode());
                qrContent.put("name", space.getName());
                qrContent.put("fullName", fullName);

                // 去掉第一层级
                String processedFullName = fullName.substring(fullName.indexOf("/") + 1);

                if (space.getType().equals(SpaceTypeEnum.POINT.name())) {
                    // 去掉最后层级
                    processedFullName = processedFullName.substring(0, processedFullName.lastIndexOf("/"));
                    // 拼接code
                    processedFullName = processedFullName + "/" + space.getCode();
                }

                // 生成缓存键 - 使用原始内容和处理后的fullName
                final String finalProcessedFullName = processedFullName;
                String cacheKey = qrContent.toJSONString() + "|" + space.getId();

                // 检查缓存中是否已存在相同内容的二维码
                return qrCodeCache.computeIfAbsent(cacheKey, key -> {
                    ImageCombiner imageCombiner = null;
                    try {
                        // 区分莫干山儿保
                        if (Objects.equals(customerCode, SysConfigConstants.CUSTOMERCODE_MGSEB)) {
                            imageCombiner = ImageCombinerUtils.generatorSpaceQrcodeMgseb(qrContent.toJSONString(),
                                finalProcessedFullName);
                        } else {
                            imageCombiner = ImageCombinerUtils.generatorSpaceQrcode(qrContent.toJSONString(),
                                finalProcessedFullName);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    // 获取图片缓存
                    BufferedImage combinedImage = imageCombiner.getCombinedImage();
                    // 写出图片缓存到字节数组中
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    try {
                        ImageIO.write(combinedImage, "png", baos);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    return baos.toByteArray();
                });
            }).collect(Collectors.toList());

            try {
                XWPFDocument document = new XWPFDocument();

                CTSectPr sectPr = document.getDocument().getBody().addNewSectPr();
                CTPageMar pageMar = sectPr.addNewPgMar();
                pageMar.setTop(BigInteger.valueOf(800)); // 设置上边距，1440代表1英寸
                pageMar.setBottom(BigInteger.valueOf(800)); // 设置下边距
                pageMar.setLeft(BigInteger.valueOf(1000)); // 设置左边距
                pageMar.setRight(BigInteger.valueOf(1000)); // 设置右边距

                // 创建一个段落对象。
                XWPFParagraph paragraph = document.createParagraph();
                // 创建一个run。run具体是什么，我也不知道。但是run是这里面的最小单元了。
                XWPFRun run = paragraph.createRun();
                // 插入图片
                spaceQrcodeExcelVOS.forEach(bytes -> {
                    try {
                        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
                        run.addPicture(byteArrayInputStream, XWPFDocument.PICTURE_TYPE_PNG, "二维码.png", Units.toEMU(235),
                            Units.toEMU(235));
                    } catch (InvalidFormatException | IOException e) {
                        throw new RuntimeException(e);
                    }
                });

                // 设置响应体内容类型
                response.setContentType("application/octet-stream");
                // 添加响应头
                response.addHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode("空间二维码.docx", "UTF-8"));
                // 暴露新添加的响应头
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

                // 使用缓冲输出流提升写入性能
                try (BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream())) {
                    document.write(bos);
                }
                // 关闭文档
                document.close();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    public static void main(String[] args) {

        // XWPFDocument document = new XWPFDocument();
        try {

            XWPFDocument document = new XWPFDocument();
            // 创建一个段落对象。
            XWPFParagraph paragraph = document.createParagraph();
            // 创建一个run。run具体是什么，我也不知道。但是run是这里面的最小单元了。
            XWPFRun run = paragraph.createRun();
            // 插入图片
            run.addPicture(new FileInputStream("d://temp/image.png"), XWPFDocument.PICTURE_TYPE_PNG, "1.png",
                Units.toEMU(400), Units.toEMU(200));
            // 创建一个输出流 即是该文档的保存位置
            OutputStream outputStream = new FileOutputStream("d://temp/output.docx");
            document.write(outputStream);
            outputStream.close();

            // FileInputStream imageStream = new FileInputStream("d://temp/image.png");
            //
            //// FileInputStream fis = new FileInputStream("image1.jpg");
            // byte[] bytes = IOUtils.toByteArray(imageStream);
            // document.addPictureData(bytes, XWPFDocument.PICTURE_TYPE_PNG);
            //// 插入图片到文档中
            // document.createParagraph().createRun().addPicture(new ByteArrayInputStream(bytes),
            // XWPFDocument.PICTURE_TYPE_PNG,
            // "image1.jpg", Units.toEMU(200), Units.toEMU(200));
            //
            // // 保存文档
            // FileOutputStream out = new FileOutputStream("d://temp/output.docx");
            // document.write(out);
            // out.close();
            // document.close();
            // imageStream.close();
            //
            // System.out.println("图片成功插入Word文档！");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 新增空间
     *
     * @param spaceSaveDTO
     */
    @Override
    public int save(SpaceSaveDTO spaceSaveDTO) {
        List<SpaceSaveDTO.CodeName> codeNames = spaceSaveDTO.getCodeNames();
        SpaceTypeEnum type = spaceSaveDTO.getType();
        switch (type) {
            case AREA:
                if (spaceMapper.selectCount(
                    Wrappers.lambdaQuery(Space.class).eq(Space::getCode, codeNames.get(0).getCode())) > 0) {
                    throw new MyRuntimeException("编码已存在！");
                }
                break;
            case BUILDING:
                if (spaceMapper.selectCount(
                    Wrappers.lambdaQuery(Space.class).eq(Space::getCode, codeNames.get(0).getCode())) > 0) {
                    throw new MyRuntimeException("编码已存在！");
                }
                break;
            case FLOOR:
                if (spaceMapper.selectCount(Wrappers.lambdaQuery(Space.class)
                    .in(Space::getCode,
                        codeNames.stream().map(SpaceSaveDTO.CodeName::getCode).collect(Collectors.toList()))
                    .eq(Space::getParentId, spaceSaveDTO.getSelectId())) > 0) {
                    throw new MyRuntimeException("编码已存在！");
                }
                break;
            case POINT:
                if (spaceMapper.selectCount(Wrappers.lambdaQuery(Space.class)
                    .in(Space::getCode,
                        codeNames.stream().map(SpaceSaveDTO.CodeName::getCode).collect(Collectors.toList()))
                    .eq(Space::getParentId, spaceSaveDTO.getSelectId())) > 0) {
                    throw new MyRuntimeException("编码已存在！");
                }
                break;
            default:
                throw new MyRuntimeException("区域类型不能为空！");
        }
        switch (type) {
            case AREA:
                return saveArea(spaceSaveDTO);
            case BUILDING:
                return saveBuilding(spaceSaveDTO);
            case FLOOR:
                return saveFloor(spaceSaveDTO);
            case POINT:
                return savePoint(spaceSaveDTO);
            default:
                throw new MyRuntimeException("区域类型不能为空！");
        }
    }

    private int saveArea(SpaceSaveDTO spaceSaveDTO) {
        List<SpaceSaveDTO.CodeName> codeNames = spaceSaveDTO.getCodeNames();
        SpaceSaveDTO.CodeName codeName = codeNames.get(0);
        String code = codeName.getCode();
        String name = codeName.getName();
        Long sort = codeName.getSort();

        Space space = new Space();
        long id = idGeneratorWrapper.nextLongId();
        space.setId(id);
        space.setSort(sort);
        space.setPath(id + "");
        space.setCode(code);
        space.setType(spaceSaveDTO.getType().name());
        space.setName(name);
        space.setFullName(name);
        space.setDeleted(GlobalDeletedFlag.NORMAL);
        return spaceMapper.insert(space);
    }

    private int saveBuilding(SpaceSaveDTO spaceSaveDTO) {
        List<SpaceSaveDTO.CodeName> codeNames = spaceSaveDTO.getCodeNames();
        SpaceSaveDTO.CodeName codeName = codeNames.get(0);
        String code = codeName.getCode();
        String name = codeName.getName();
        Long sort = codeName.getSort();

        Space space = new Space();
        long id = idGeneratorWrapper.nextLongId();
        space.setId(id);
        // 查询上级区域
        Space spaceArea = spaceMapper.selectById(spaceSaveDTO.getSelectId());
        checkIsNull(spaceArea, "区域不存在！");

        space.setParentId(spaceArea.getId());
        space.setPath(spaceArea.getPath() + "/" + id);
        space.setCode(code);
        space.setType(spaceSaveDTO.getType().name());
        space.setName(name);
        space.setFullName(spaceArea.getFullName() + "/" + name);
        space.setDeleted(GlobalDeletedFlag.NORMAL);
        space.setSort(sort);
        return spaceMapper.insert(space);
    }

    private int saveFloor(SpaceSaveDTO spaceSaveDTO) {
        List<SpaceSaveDTO.CodeName> codeNames = spaceSaveDTO.getCodeNames();
        Space buildingSpace = spaceMapper.selectById(spaceSaveDTO.getSelectId());

        Long userId = TokenData.takeFromRequest().getUserId();
        List<Space> spaces = new ArrayList<>();
        for (SpaceSaveDTO.CodeName codeName : codeNames) {
            String code = codeName.getCode();
            String name = codeName.getName();
            Long sort = codeName.getSort();
            // 编码或名称为空，则跳过
            if (StringUtils.isBlank(code) || StringUtils.isBlank(name)) {
                continue;
            }
            Space space = new Space();
            long id = idGeneratorWrapper.nextLongId();
            space.setId(id);
            space.setParentId(buildingSpace.getId());
            space.setPath(buildingSpace.getPath() + "/" + id);
            space.setCode(code);
            space.setType(spaceSaveDTO.getType().name());
            space.setName(name);
            space.setFullName(buildingSpace.getFullName() + "/" + name);
            space.setDeleted(GlobalDeletedFlag.NORMAL);
            Date date = new Date();
            space.setCreateUserId(userId);
            space.setCreateTime(date);
            space.setUpdateUserId(userId);
            space.setUpdateTime(date);
            space.setSort(sort);
            space.setModelThreeDimensional(codeName.getModelThreeDimensional());
            space.setModelTwoDimensional(codeName.getModelTwoDimensional());
            spaces.add(space);
        }
        // 批量插入
        return spaceMapper.insertBatch(spaces);
    }

    private int savePoint(SpaceSaveDTO spaceSaveDTO) {
        List<SpaceSaveDTO.CodeName> codeNames = spaceSaveDTO.getCodeNames();
        Space parentSpace = spaceMapper.selectById(spaceSaveDTO.getSelectId());
        checkIsNull(parentSpace, "上级空间不存在！");

        Long userId = TokenData.takeFromRequest().getUserId();
        List<Space> spaces = new ArrayList<>();
        for (SpaceSaveDTO.CodeName codeName : codeNames) {
            String code = codeName.getCode();
            String name = codeName.getName();
            String commonArea = codeName.getCommonArea();
            Long sort = codeName.getSort();

            Space space = new Space();
            long id = idGeneratorWrapper.nextLongId();
            space.setId(id);
            space.setParentId(parentSpace.getId());
            space.setPath(parentSpace.getPath() + "/" + id);
            space.setCode(code);
            space.setType(spaceSaveDTO.getType().name());
            space.setName(name);
            space.setFullName(parentSpace.getFullName() + "/" + name);
            space.setCoordinate(codeName.getCoordinate());
            space.setDeleted(GlobalDeletedFlag.NORMAL);
            Date date = new Date();
            space.setCreateUserId(userId);
            space.setCreateTime(date);
            space.setUpdateUserId(userId);
            space.setUpdateTime(date);
            space.setCommonArea(commonArea);
            space.setSort(sort);
            spaces.add(space);
        }
        return spaceMapper.insertBatch(spaces);
    }

    private void checkIsNull(Object value, String errorMessage) {
        if (value == null) {
            throw new MyRuntimeException(errorMessage);
        }
        if (value instanceof String && StringUtils.isBlank((String)value)) {
            throw new MyRuntimeException(errorMessage);
        }
        if (value instanceof Collection && ((Collection<?>)value).isEmpty()) {
            throw new MyRuntimeException(errorMessage);
        }
    }

    @Override
    public List<SpaceVO> listSpace(SpaceQueryDTO spaceQuery) {
        List<Space> spaces;
        LambdaQueryWrapper<Space> queryWrapper = Wrappers.lambdaQuery(Space.class).orderByAsc(Space::getCode);
        spaces = spaceMapper.selectList(queryWrapper);
        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");
        treeNodeConfig.setNameKey("name");
        treeNodeConfig.setChildrenKey("children");
        // 排序字段
        treeNodeConfig.setWeightKey("sort");
        // 最大递归深度
        treeNodeConfig.setDeep(spaceQuery.getDeep());
        // 转换器
        List<Tree<String>> treeList = TreeUtil.build(spaces, null, treeNodeConfig, (treeNode, tree) -> {
            tree.setId(treeNode.getId().toString());
            tree.setParentId(treeNode.getParentId() == null ? null : treeNode.getParentId().toString());
            tree.setName(treeNode.getName());
            // 扩展属性 ...
            tree.putExtra("code", treeNode.getCode());
            tree.putExtra("path", treeNode.getPath());
            tree.putExtra("fullName", treeNode.getFullName());
            tree.putExtra("deleted", treeNode.getDeleted());
            tree.putExtra("type", treeNode.getType());
            tree.putExtra("sort", treeNode.getSort());
            tree.putExtra("createUserId", treeNode.getCreateUserId());
            tree.putExtra("createTime", treeNode.getCreateTime());
            tree.putExtra("updateUserId", treeNode.getUpdateUserId());
            tree.putExtra("updateTime", treeNode.getUpdateTime());
        });
        List<SpaceVO> spacelList = new ArrayList<>();
        treeList.forEach(one -> {
            if (CollectionUtils.isNotEmpty(one.getChildren())) {
                one.getChildren().forEach(two -> {
                    if (CollectionUtils.isNotEmpty(two.getChildren())) {
                        two.getChildren().forEach(three -> {
                            if (CollectionUtils.isNotEmpty(three.getChildren())) {
                                three.getChildren().forEach(four -> {
                                    Tree<String> stringTree = four.cloneTree();
                                    String fullName = (String)stringTree.get("fullName");
                                    if (StringUtils.isNotBlank(fullName)
                                        && fullName.contains(spaceQuery.getFullName())) {
                                        System.out.println(fullName);
                                        SpaceVO spaceVO = new SpaceVO();
                                        spaceVO.setFullName(fullName);
                                        spaceVO.setCode((String)stringTree.get("code"));
                                        spaceVO.setId(Long.valueOf((String)stringTree.get("id")));
                                        spaceVO.setName((String)stringTree.get("name"));
                                        spaceVO.setPath((String)stringTree.get("path"));
                                        spaceVO.setParentId(Long.valueOf((String)stringTree.get("parentId")));
                                        spacelList.add(spaceVO);
                                    }
                                });
                            }
                        });
                    }
                });
            }
        });
        return spacelList;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description系统点位维护，信息变更，关联设备，设施详情页面安装位置同步变更，异步修改
     * @Date 上午11:41 2024/9/23
     * @Param [spaceUpdate]
     **/
    @Async("applicationTaskExecutor")
    public void asyncUpdateSpaceFullName(SpaceUpdateDTO spaceUpdate) {
        log.info("async update SpaceFullName，request: {}", JSONObject.toJSON(spaceUpdate));
        LambdaQueryWrapper<DeviceBaseStation> equWrapper =
            Wrappers.lambdaQuery(DeviceBaseStation.class).like(DeviceBaseStation::getSpacePath, spaceUpdate.getId())
                .eq(DeviceBaseStation::getIsDelete, GlobalDeletedFlag.NORMAL);
        Long total = deviceBaseStationMapper.selectCount(equWrapper);
        int pageNo = 1;
        int pageSize = 500;
        IPage<DeviceBaseStation> pageData = getEquipmentIPage(pageNo, pageSize, spaceUpdate.getId());
        if (CollectionUtils.isNotEmpty(pageData.getRecords())) {
            for (int i = 0; i < (total % pageSize > 0 ? total / pageSize + 1 : total / pageSize); i++) {
                Map<Long, String> equipmentMap = new HashMap<>();
                IPage<DeviceBaseStation> equipmentIPage = getEquipmentIPage(i + 1, pageSize, spaceUpdate.getId());
                if (CollectionUtils.isNotEmpty(equipmentIPage.getRecords())) {
                    List<DeviceBaseStation> equipmentList = equipmentIPage.getRecords();
                    if (CollectionUtils.isNotEmpty(equipmentList)) {
                        equipmentList.forEach(equipment -> {
                            Long equipmentId = equipment.getId();
                            // 解析原始数据
                            String spacePath = equipment.getSpacePath();
                            String spaceFullName = equipment.getSpaceFullName();
                            String[] ids = spacePath.split("/");
                            String[] names = spaceFullName.split("/");
                            StringBuilder sb = new StringBuilder();
                            for (int j = 0; j < ids.length; j++) {
                                String spacePathId = ids[j];
                                if (spacePathId.equals(spaceUpdate.getId().toString())) {
                                    sb.append(spaceUpdate.getName());
                                    sb.append("/");
                                } else {
                                    sb.append(names[j]);
                                    sb.append("/");
                                }
                            }
                            String path = sb.toString();
                            String trimPath = "";
                            if (StringUtils.isNotBlank(path)) {
                                trimPath = path.replaceAll("(^/+)|(/+$)", "");
                                equipmentMap.put(equipmentId, trimPath);
                            }
                        });
                    }
                }
//                log.info("async batchUpdateMap SpaceFullName，request: {}", JSON.toString(equipmentMap));
                // TODO
                // deviceBaseStationMapper.batchUpdateMap(equipmentMap);
            }
        }
    }

    @Override
    public SpaceVO getSpaceById(Long id) {
        log.info("getSpaceById,id{}", id);
        SpaceVO spaceVO = new SpaceVO();
        Space space = spaceMapper.selectById(id);
        if (null != space) {
            spaceVO = MyModelUtil.copyTo(space, SpaceVO.class);
        }
        return spaceVO;
    }

    @Override
    public ResponseResult<List<Tree<String>>> listTree(Long equipmentTypeId) {
        List<DeviceBaseStation> equipmentList = new LambdaQueryChainWrapper<>(deviceBaseStationMapper)
            .eq(DeviceBaseStation::getDeviceBaseStationType, equipmentTypeId).isNotNull(DeviceBaseStation::getSpacePath)
            .list();
        List<Long> spaceIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(equipmentList)) {
            List<String> spacePathList =
                equipmentList.stream().map(DeviceBaseStation::getSpacePath).collect(Collectors.toList());
            spacePathList.forEach(path -> {
                String[] split = path.split("/");
                Arrays.asList(split).forEach(id -> {
                    spaceIdList.add(Long.parseLong(id));
                });
            });
        }

        List<Long> collect = spaceIdList.stream().distinct().collect(Collectors.toList());
        LambdaQueryWrapper<Space> queryWrapper = Wrappers.lambdaQuery(Space.class).orderByAsc(Space::getCode)
            .in(Space::getId, collect).in(Space::getType, "BUILDING", "FLOOR", "AREA");
        List<Space> spaces = spaceMapper.selectList(queryWrapper);

        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");
        treeNodeConfig.setNameKey("name");
        treeNodeConfig.setChildrenKey("children");
        // 排序字段
        treeNodeConfig.setWeightKey("sort");
        // 转换器
        List<Tree<String>> treeList = TreeUtil.build(spaces, null, treeNodeConfig, (treeNode, tree) -> {
            tree.setId(treeNode.getId().toString());
            tree.setParentId(treeNode.getParentId() == null ? null : treeNode.getParentId().toString());
            tree.setName(treeNode.getName());
            // 扩展属性 ...
            tree.putExtra("type", treeNode.getType());
            tree.putExtra("path", treeNode.getPath());
            tree.putExtra("sort", treeNode.getSort());
        });
        return ResponseResult.success(treeList);
    }

    @Override
    public List<SpaceVO> getSpace(SpaceQueryVO spaceQueryVO) {
        LambdaQueryWrapper<Space> queryWrapper = Wrappers.lambdaQuery(Space.class).orderByAsc(Space::getCode)
            .like(StringUtils.isNotBlank(spaceQueryVO.getBuildingName()), Space::getFullName,
                spaceQueryVO.getBuildingName())
            .like(StringUtils.isNotBlank(spaceQueryVO.getFlowName()), Space::getFullName, spaceQueryVO.getFlowName())
            .like(StringUtils.isNotBlank(spaceQueryVO.getPointName()), Space::getFullName, spaceQueryVO.getPointName());
        List<Space> spaceList = spaceMapper.selectList(queryWrapper);
        return Space.INSTANCE.fromModelList(spaceList);
    }

    @Cacheable(value = RedisKeyConstant.BASEINFO_SPACE, key = "#id")
    public Space getSpaceCacheById(Long id) {
        return spaceMapper.selectById(id);
    }

    @Override
    public List<Tree<String>> spaceTreeByTypeId(Long deviceTypeId) {
       List<Device> deviceList = deviceMapper.selectList(Wrappers.lambdaQuery(Device.class)
               .eq(Device::getDeviceTerminalTypeId,deviceTypeId).isNotNull(Device::getSpacePath)
               .eq(Device::getIsDelete,GlobalDeletedFlag.NORMAL));

        Set<Long> allSpaceIds = new HashSet<>();
        for (Device device : deviceList) {
            if (StringUtils.isNotBlank(device.getSpacePath())) {
                Arrays.stream(device.getSpacePath().split("/"))
                        .map(Long::valueOf)
                        .forEach(allSpaceIds::add);
            }
        }

        if (allSpaceIds.isEmpty()) {
            return Collections.emptyList();
        }

        List<Space> spaceList = new ArrayList<>();
        CollUtil.split(allSpaceIds, 500).forEach(batch -> {
            spaceList.addAll(spaceMapper.selectBatchIds(batch));
        });

        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("parentId");
        treeNodeConfig.setNameKey("name");
        treeNodeConfig.setChildrenKey("children");
        // 排序字段
        treeNodeConfig.setWeightKey("sort");
        // 转换器
        List<Tree<String>> treeList = TreeUtil.build(spaceList, null, treeNodeConfig, (treeNode, tree) -> {
            tree.setId(treeNode.getId().toString());
            tree.setParentId(treeNode.getParentId() == null ? null : treeNode.getParentId().toString());
            tree.setName(treeNode.getName());
            // 扩展属性 ...
            tree.putExtra("type", treeNode.getType());
            tree.putExtra("path", treeNode.getPath());
            tree.putExtra("sort", treeNode.getSort());
        });

        return treeList;
    }
}
