package com.rutong.medical.admin.dto.alarm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * @ClassName AlarmDetailMonitorImgDTO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/29 11:47
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@ApiModel("AlarmDetailMonitorImgDTO对象")
@Data
public class AlarmDetailMonitorImgDTO {

    @ApiModelProperty(value = "报警监控照片表ID")
    @NotNull(message = "数据验证失败，报警监控照片表ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "报警记录表ID")
    private Long alarmDetailId;

    @ApiModelProperty(value = "视频监控表ID")
    private Long deviceMonitorId;

    @ApiModelProperty(value = "照片路径")
    private String imgPath;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
