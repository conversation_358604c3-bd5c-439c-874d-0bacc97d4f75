package com.rutong.medical.admin.vo.location;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName userLocationIndexStat
 * @Description
 * <AUTHOR>
 * @Date 2025/7/23 14:03
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Data
@ApiModel("人员定位首页各统计值")
public class userLocationIndexStatVO {

    @ApiModelProperty(value = "医护人数数量")
    private Integer medicalCount;

    @ApiModelProperty(value = "报警按钮数量")
    private Integer buttonCount;

    @ApiModelProperty(value = "基站数量")
    private Integer baseStationCount;

    @ApiModelProperty(value = "保安数量")
    private Integer securityCount;

    @ApiModelProperty(value = "全院人数")
    private Integer hospitalCount;
}
