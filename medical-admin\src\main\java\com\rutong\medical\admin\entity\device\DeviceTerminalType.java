package com.rutong.medical.admin.entity.device;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rutong.medical.admin.vo.device.DeviceTerminalTypeVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备分类对象 sm_device_terminal_type
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sm_device_terminal_type")
public class DeviceTerminalType extends BaseModel {

    /** 设备分类表ID */
    @TableId(value = "id")
    private Long id;

    /** 编号 */
    private String typeCode;

    /** 名称 */
    private String typeName;

    /** 父级id */
    private Long parentId;

    /** id路径 */
    private String pathId;

    /** 是否删除 */
    private Integer isDelete;

    /**
     * 图标
     */
    private String iconPath;

    @Mapper
    public interface SmDeviceTerminalTypeModelMapper extends BaseModelMapper<DeviceTerminalTypeVO, DeviceTerminalType> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        DeviceTerminalType toModel(DeviceTerminalTypeVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        DeviceTerminalTypeVO fromModel(DeviceTerminalType entity);
    }

    public static final SmDeviceTerminalTypeModelMapper INSTANCE =
        Mappers.getMapper(SmDeviceTerminalTypeModelMapper.class);
}
