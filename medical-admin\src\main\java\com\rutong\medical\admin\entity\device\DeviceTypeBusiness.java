package com.rutong.medical.admin.entity.device;

import com.rutong.medical.admin.vo.device.DeviceTypeBusinessVO;
import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * 设备分类业务对象 device_type_business
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@TableName(value = "sm_device_type_business")
public class DeviceTypeBusiness{

    /** 设备分类业务表ID */
    @TableId(value = "id")
    private Long id;

    /** 设备分类表编号 */
    private String typeCode;

    /** 业务编号 */
    private String businessCode;


    @Mapper
    public interface DeviceTypeBusinessModelMapper extends BaseModelMapper<DeviceTypeBusinessVO, DeviceTypeBusiness> {
        /**
        * 转换Vo对象到实体对象。
        *
        * @param entityVo 域对象。
        * @return 实体对象。
        */
        @Override
        DeviceTypeBusiness toModel(DeviceTypeBusinessVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        DeviceTypeBusinessVO fromModel(DeviceTypeBusiness entity);
    }

    public static final DeviceTypeBusinessModelMapper INSTANCE = Mappers.getMapper(DeviceTypeBusinessModelMapper.class);
}
