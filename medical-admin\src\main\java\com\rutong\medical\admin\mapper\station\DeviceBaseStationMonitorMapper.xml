<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rutong.medical.admin.mapper.station.DeviceBaseStationMonitorMapper">
    <resultMap type="com.rutong.medical.admin.entity.station.DeviceBaseStationMonitor" id="SmDeviceBaseStationMonitorResult">
        <result property="id" column="id" />
        <result property="deviceBaseStationId" column="device_base_station_id" />
        <result property="deviceMonitorId" column="device_monitor_id" />
    </resultMap>

    <sql id="selectSmDeviceBaseStationMonitorVo">
        select id, device_base_station_id, device_monitor_id from sm_device_base_station_monitor
    </sql>

</mapper>