package com.rutong.medical.admin.controller.alarm;

import com.rutong.medical.admin.dto.alarm.AlarmDetailPageQueryDTO;
import com.rutong.medical.admin.dto.alarm.AlarmDisposeDTO;
import com.rutong.medical.admin.service.alarm.AlarmDetailService;
import com.rutong.medical.admin.vo.alarm.AlarmDetailTDVO;
import com.rutong.medical.admin.vo.alarm.AlarmDetailVO;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.object.MyPageData;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName AlarmDetailController
 * @Description
 * <AUTHOR>
 * @Date 2025/7/22 17:07
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Api(tags = "报警记录")
@RestController
@RequestMapping("/alarm")
@AllArgsConstructor
public class AlarmDetailController {

    private AlarmDetailService alarmDetailService;

    /**
     * 根据业务编号获取所有报警记录
     *
     * @return
     */
    @ApiOperation("根据业务编号获取所有报警记录")
    @GetMapping("/getAlarmDetailList")
    public ResponseResult<List<AlarmDetailTDVO>> getAlarmDetailList(String businessCode) {
        return ResponseResult.success(alarmDetailService.getAlarmDetailList(businessCode));
    }

    /**
     * 分页获取报警记录列表
     *
     * @return
     */
    @ApiOperation("分页获取报警记录")
    @GetMapping("/page")
    public ResponseResult<MyPageData<AlarmDetailVO>> page(AlarmDetailPageQueryDTO alarmDetailPageQueryDTO) {
        return ResponseResult.success(alarmDetailService.page(alarmDetailPageQueryDTO));
    }

    /**
     * 处理报警
     *
     * @return
     */
    @ApiOperation("处理报警")
    @PostMapping("/dispose")
    public ResponseResult<Void> dispose(@RequestBody AlarmDisposeDTO alarmDisposeDTO) {
        return alarmDetailService.dispose(alarmDisposeDTO) == true ? ResponseResult.success() :
                ResponseResult.error(ErrorCodeEnum.SERVER_INTERNAL_ERROR);
    }

    /**
     * 报警详情页
     *
     * @return
     */
    @ApiOperation("报警详情页")
    @GetMapping("/detail")
    public ResponseResult<AlarmDetailVO> detail(@RequestParam(required = true) Long id) {
        return ResponseResult.success(alarmDetailService.detail(id));
    }

}
