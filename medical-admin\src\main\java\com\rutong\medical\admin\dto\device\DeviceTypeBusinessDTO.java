package com.rutong.medical.admin.dto.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.soft.common.core.validator.UpdateGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

/**
 * DeviceTypeBusinessDTO对象
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@ApiModel("DeviceTypeBusinessDTO对象")
@Data
public class DeviceTypeBusinessDTO {

    @ApiModelProperty(value = "设备分类业务表ID")
    @NotNull(message = "数据验证失败，设备分类业务表ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "设备分类表编号")
    private String typeCode;

    @ApiModelProperty(value = "业务编号")
    private String businessCode;

}
