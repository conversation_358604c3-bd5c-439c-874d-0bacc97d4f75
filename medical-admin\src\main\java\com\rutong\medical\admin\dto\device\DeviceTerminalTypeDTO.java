package com.rutong.medical.admin.dto.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("DeviceTerminalTypeDTO")
@Data
public class DeviceTerminalTypeDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private Long parentId;

    /** 名称 */
    @ApiModelProperty(value = "typeName")
    private String typeName;

    @ApiModelProperty(value = "图标")
    private String iconPath;

    @ApiModelProperty(value = "业务系统编号")
    private String businessCode;

    @ApiModelProperty(value = "f分类编码")
    private String typeCode;
}
