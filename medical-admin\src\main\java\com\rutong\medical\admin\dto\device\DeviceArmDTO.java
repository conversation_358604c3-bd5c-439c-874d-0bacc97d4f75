package com.rutong.medical.admin.dto.device;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 设备布防DTO
 *
 * <AUTHOR>
 * @Date 2025-08-01
 */
@Data
@ApiOperation(value = "smDeviceLayoutDefence DTO对象")
public class DeviceArmDTO {

    @ApiModelProperty(value = "设备布防表ID")
    private Long id;

    @ApiModelProperty(value = "设备表ID")
    private Long deviceId;

    @ApiModelProperty(value = "防区表ID")
    private Long invadeDefenceId;

    @ApiModelProperty(value = "布防状态")
    private Integer defenceState;

    @ApiModelProperty(value = "布防开始时间")
    private LocalTime startTime;

    @ApiModelProperty(value = "布防结束时间")
    private LocalTime endTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUserId;

    @ApiModelProperty(value = "更新时间")
    private LocalDate updateTime;

}
