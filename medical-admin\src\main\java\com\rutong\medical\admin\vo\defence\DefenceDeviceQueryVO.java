package com.rutong.medical.admin.vo.defence;

import lombok.Data;

/**
 * 防区设备查询结果VO - 用于接收数据库查询结果
 * <AUTHOR>
 * @Date 2025-07-30
 */
@Data
public class DefenceDeviceQueryVO {

    /**
     * 防区ID
     */
    private Long invadeDefenceId;

    /**
     * 防区编号
     */
    private String defenceCode;

    /**
     * 防区名称
     */
    private String defenceName;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 终端SN
     */
    private String deviceSn;

    /**
     * 设备位置
     */
    private String spaceFullName;
}
