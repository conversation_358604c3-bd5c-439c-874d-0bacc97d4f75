package com.rutong.medical.admin.mapper.location;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.rutong.medical.admin.entity.location.UserLocation;
import com.rutong.medical.admin.vo.location.UserLocationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName UserLocationMapper
 * @Description 人员定位
 * <AUTHOR>
 * @Date 2025/7/15 11:38
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@DS("tdengine")
public interface UserLocationTDMapper {

    /**
     * 工卡定位数据
     *
     * @param userLocation
     * @return
     */
    int cardInsert(UserLocation userLocation);

    /**
     * 报警器数据
     *
     * @param userLocation
     * @return
     */
    int buttonInsert(UserLocation userLocation);

    /**
     * 根据设备sn查询人员定位数据
     *
     * @param deviceSn
     * @return
     */
    UserLocationVO selectUserLocationByDeviceSn(@Param("deviceSn") String deviceSn);

    /**
     * 根据楼层id查询人员定位数据
     *
     * @param floorId
     * @return
     */
    public List<UserLocationVO> selectUserLocationByFloor(@Param("floorId") Long floorId,@Param("tagCode") String tagCode);

}
