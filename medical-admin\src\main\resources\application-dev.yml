spring:
  datasource:
    dynamic:
      datasource:
        operation-log:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *************************************************************************************************************
          username: root
          password: root123456
        tdengine:
          type: com.zaxxer.hikari.HikariDataSource
          driverClassName: com.taosdata.jdbc.rs.RestfulDriver
          url: jdbc:TAOS-RS://*************:6041/medical_iot
          jdbc-url: jdbc:TAOS-RS://*************:6041/medical_iot
          username: root
          password: taosdata
          hikari:
            max-pool-size: 5
            connection-timeout: 30000
            idle-timeout: 600000
            max-lifetime: 1800000
      primary: sharding-data-source

  shardingsphere:
    datasource:
      ## 数据源别名
      names: main
      # 数据库链接 [主数据源]
      main:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ************************************************************************************************************************************
        username: root
        password: root123456
        initialSize: 10
        minIdle: 10
        maxActive: 50
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        maxOpenPreparedStatements: 20
        validationQuery: SELECT 'x'
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        filters:
          stat:
            enabled: false
        useGlobalDataSourceStat: true
        web-stat-filter:
          enabled: false
          url-pattern: /*
          exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*,/actuator/*"
        stat-view-servlet:
          enabled: false
          urlPattern: /druid/*
          resetEnable: true
    rules:
      sharding:
        tables:
          sp_energy_data: ## 数据库表名前缀
            ## 实际表名
            actual-data-nodes: main.sp_energy_data
            table-strategy: ## 分表策略
              complex:
                ## 按照分表的列
                sharding-columns: equipment_type,record_date,record_year
                ## 分表算法名称(使用 yml 配置不能包含下划线,否则不生效)
                sharding-algorithm-name: energy-year-sharding-algorithm
          sp_energy_remind: ## 数据库表名前缀
            ## 实际表名
            actual-data-nodes: main.sp_energy_remind
            table-strategy: ## 分表策略
              complex:
                ## 按照分表的列
                sharding-columns: record_date,record_year
                ## 分表算法名称(使用 yml 配置不能包含下划线,否则不生效)
                sharding-algorithm-name: energy-year-sharding-algorithm
    props:
      ## 展示执行 SQL
      sql-show: false

redis:
  # redisson的配置。每个服务可以自己的配置文件中覆盖此选项。
  redisson:
    # 如果该值为false，系统将不会创建RedissionClient的bean。
    enabled: true
    # mode的可用值为，single/cluster/sentinel/master-slave
    mode: single
    # single: 单机模式
    address: redis://*************:6379
    # 库
    database: 3
    # 链接超时，单位毫秒。
    timeout: 6000
    # 单位毫秒。分布式锁的超时检测时长。
    # 如果一次锁内操作超该毫秒数，或在释放锁之前异常退出，Redis会在该时长之后主动删除该锁使用的key。
    lockWatchdogTimeout: 60000
    # redis 密码，空可以不填。
    password: rutong123456
    pool:
      # 连接池数量。
      poolSize: 20
      # 连接池中最小空闲数量。
      minIdle: 5

mqtt:
  hostUrl: tcp://*************:1883
  username: admin
  password: public
  clientId: smart_park_local_${random.uuid}
  cleanSession: true
  reconnect: true
  timeout: 100
  keepAlive: 100

xxl:
  job:
    admin:
      addresses: http://*************:7777/xxl-job-admin
    accessToken: default_token
    executor:
      appname: smart-park-mgs
      address:
      logpath: /logs/application-webadmin
      logretentiondays: 30

flowable:
  async-executor-activate: false
  database-schema-update: false

oa:
  type: DING_TALK
  ding-talk:
    enable: true
    serverUrl: https://oapi.dingtalk.com
    appKey: dingggrbr9yrc92sn7n9
    appSecret: n6LRRZJiLDp8BRICWHJX6kBNNwJBV6x62gM3dTH-1kAVDwTNnz88F8hLWs2tzjBh
    # 移动端应用地址
    appUrl: http://*************:5173/#

config:
  #文件上传路径
  uploadPath: ./files
server:
  port: 8888

logging:
  level:
    # 总的日志级别,方便设置logback
    root: info
    # 这里设置的日志级别优先于logback-spring.xml文件Loggers中的日志级别。
    com.soft: debug
    com.rutong.medical.admin: debug
    org.springframework.web.client: debug
  config: classpath:logback-spring.xml



swagger:
  # 当enabled为false的时候，则可禁用swagger。
  enabled: true
  # 工程的基础包名。
  basePackage: com.rutong
  # 工程服务的基础包名。
  serviceBasePackage: com.rutong
  title: 医疗物联网
  description: 医疗物联网管理平台
  version: 1.0


#流媒体服务，视频转码使用
mediaService:
  ip: *************:9500
  secret: 035c73f7-bb6b-4889-a715-d9eb2d1925cc


