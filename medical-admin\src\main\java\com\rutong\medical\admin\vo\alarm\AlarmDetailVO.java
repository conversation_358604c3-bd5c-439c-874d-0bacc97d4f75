package com.rutong.medical.admin.vo.alarm;

import java.util.Date;

import com.rutong.medical.admin.constant.AlarmTypeEnum;
import com.rutong.medical.admin.constant.DeviceTypeEnum;
import com.rutong.medical.admin.constant.WhetherConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Date;
import java.util.List;

/**
 * @ClassName AlarmDetailVO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 14:39
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@ApiModel("AlarmDetailVO视图对象")
@Data
public class AlarmDetailVO {

    @ApiModelProperty(value = "报警记录表ID")
    private Long id;

    @ApiModelProperty(value = "基站表ID")
    private Long deviceBaseStationId;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "设备ID")
    private Long deviceId;

    @ApiModelProperty(value = "设备sn")
    private String deviceSn;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "业务系统编号")
    private String businessCode;

    @ApiModelProperty(value = "设备分类表code")
    private String deviceTypeCode;

    @ApiModelProperty(value = "分类名称")
    private String deviceTypeCodeName;

    @ApiModelProperty(value = "报警类型(1-低电压, 2-按键, 3-防拆, 4-红外入侵)")
    private Integer alarmType;

    @ApiModelProperty(value = "报警类型名称")
    private String alarmTypeName;

    @ApiModelProperty(value = "处理状态(0:未确认,1:已确认)")
    private Byte disposeState;

    @ApiModelProperty(value = "处理状态名称")
    private String disposeStateName;

    @ApiModelProperty(value = "上报位置ID")
    private Long spaceId;

    @ApiModelProperty(value = "上报位置名称")
    private String spaceFullName;

    @ApiModelProperty(value = "上报时间")
    private Date alarmTime;

    @ApiModelProperty(value = "处理时间")
    private Date disposeTime;

    @ApiModelProperty(value = "报警监控照片")
    private List<AlarmDetailMonitorImgVO> alarmDetailMonitorImgList;

    public void setDisposeState(Byte disposeState) {
        this.disposeState = disposeState;
        if (ObjectUtils.isNotEmpty(disposeState)) {
            this.disposeStateName = disposeState == WhetherConstant._YES ? "已确认" : "未确认";
        }
    }

    public void setAlarmType(Integer alarmType) {
        this.alarmType = alarmType;
            if (ObjectUtils.isNotEmpty(alarmType)){
           this.alarmTypeName = AlarmTypeEnum.getName(alarmType);
        }
    }

    public void setDeviceTypeCode(String deviceTypeCode) {
        this.deviceTypeCode = deviceTypeCode;
        if (ObjectUtils.isNotEmpty(deviceTypeCode)){
            this.deviceTypeCodeName = DeviceTypeEnum.getName(Integer.valueOf(deviceTypeCode));
        }
    }
}
