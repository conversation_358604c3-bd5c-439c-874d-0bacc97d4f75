package com.rutong.medical.admin.service.alarm.impl;
import com.rutong.medical.admin.entity.alarm.AlarmDetailMonitorImg;
import com.rutong.medical.admin.mapper.alarm.AlarmDetailMonitorImgMapper;
import com.rutong.medical.admin.service.alarm.AlarmDetailMonitorImgService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName AlarmDetailMonitorImgServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/7/29 11:50
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Service
public class AlarmDetailMonitorImgServiceImpl extends ServiceImpl<AlarmDetailMonitorImgMapper, AlarmDetailMonitorImg> implements AlarmDetailMonitorImgService {

    @Autowired
    private AlarmDetailMonitorImgMapper alarmDetailMonitorImgMapper;

}
