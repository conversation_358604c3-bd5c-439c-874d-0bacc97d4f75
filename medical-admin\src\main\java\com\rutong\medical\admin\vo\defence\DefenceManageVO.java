package com.rutong.medical.admin.vo.defence;

import com.soft.common.core.object.MyPageParam;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-21
 */

@Data
public class DefenceManageVO extends MyPageParam {

    /**
     * 防区基本信息
     */
    private BasicInfo basicInfo;

    /**
     * 设备列表
     */
    private List<DeviceInfo> deviceList;

    @Data
    public static class BasicInfo {

        /**
         * 防区ID
         */
        private Long invadeDefenceId;

        /**
         * 防区编号
         */
        private String defenceCode;

        /**
         * 防区名称
         */
        private String defenceName;

    }


    @Data
    public static class DeviceInfo {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 设备名称
         */
        private String deviceName;

        /**
         * 终端SN
         */
        private String deviceSn;

        /**
         * 设备位置
         */
        private String spaceFullName;
    }

}
