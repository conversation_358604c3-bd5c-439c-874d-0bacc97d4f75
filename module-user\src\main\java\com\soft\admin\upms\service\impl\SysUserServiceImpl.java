package com.soft.admin.upms.service.impl;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.soft.admin.upms.enums.TagEnum;
import com.soft.common.core.constant.RedisKeyConstant;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.page.PageMethod;
import com.soft.admin.upms.api.dingtalk.service.IOaUserApi;
import com.soft.admin.upms.config.UserApplicationConfig;
import com.soft.admin.upms.dao.*;
import com.soft.admin.upms.dto.OaNoPasswordLoginDTO;
import com.soft.admin.upms.dto.SysUserQueryDTO;
import com.soft.admin.upms.dto.SysUserSaveOrUpdateDTO;
import com.soft.admin.upms.enums.OaTypeEnums;
import com.soft.admin.upms.model.*;
import com.soft.admin.upms.model.constant.SysMenuType;
import com.soft.admin.upms.model.constant.SysOnlineMenuPermType;
import com.soft.admin.upms.model.constant.SysUserStatus;
import com.soft.admin.upms.model.constant.SysUserType;
import com.soft.admin.upms.service.*;
import com.soft.admin.upms.vo.SysMenuPerm;
import com.soft.admin.upms.vo.SysUserDeptVO;
import com.soft.admin.upms.vo.SysUserDetailVO;
import com.soft.admin.upms.vo.SysUserListVO;
import com.soft.common.core.base.dao.BaseDaoMapper;
import com.soft.common.core.base.service.BaseService;
import com.soft.common.core.constant.ApplicationConstant;
import com.soft.common.core.constant.ErrorCodeEnum;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.object.*;
import com.soft.common.core.util.*;
import com.soft.common.datafilter.config.DataFilterProperties;
import com.soft.common.ext.base.BizWidgetDatasource;
import com.soft.common.ext.constant.BizWidgetDatasourceType;
import com.soft.common.ext.util.BizWidgetDatasourceExtHelper;
import com.soft.common.online.api.config.OnlineApiProperties;
import com.soft.common.online.config.OnlineProperties;
import com.soft.common.online.model.OnlineDatasource;
import com.soft.common.online.service.OnlineDatasourceService;
import com.soft.common.online.util.OnlineUtil;
import com.soft.common.sequence.wrapper.IdGeneratorWrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户管理数据操作服务类。
 *
 * <AUTHOR>
 * @date 2022-07-12
 */
@Slf4j
@Service("sysUserService")
public class SysUserServiceImpl extends BaseService<SysUser, Long> implements SysUserService, BizWidgetDatasource {
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysUserPostMapper sysUserPostMapper;
    @Autowired
    private SysDataPermUserMapper sysDataPermUserMapper;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SysUserDeptMapper sysUserDeptMapper;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysDataPermService sysDataPermService;
    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private BizWidgetDatasourceExtHelper bizWidgetDatasourceExtHelper;
    @Resource
    private SysDeptPostMapper sysDeptPostMapper;
    @Resource
    private SysTagMapper sysTagMapper;
    @Resource
    private SysUserTagMapper sysUserTagMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserApplicationConfig appConfig;
    @Autowired
    private HttpServletRequest httpServletRequest;
    @Autowired
    private DataFilterProperties dataFilterProperties;
    @Autowired
    private SysPermWhitelistService sysPermWhitelistService;
    @Autowired
    private OnlineDatasourceService onlineDatasourceService;
    @Autowired
    private OnlineProperties onlineProperties;
    @Autowired
    private OnlineApiProperties onlineApiProperties;
    @Autowired
    private SysPostService sysPostService;
    @Autowired
    private SysPermCodeService sysPermCodeService;
    @Autowired
    private SysMenuService sysMenuService;
    @Autowired
    private SysPermService sysPermService;
    @Autowired
    private SysUserService sysUserService;
    @Resource
    private IOaUserApi iOaUserApi;
    @Resource
    private SysConfigMapper sysConfigMapper;

    public static final String DEFAULT_PASSWORD = "defaultPassword";

    public static final String RESET_PASSWORD = "resetPassword";

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SysUser> mapper() {
        return sysUserMapper;
    }

    @PostConstruct
    private void registerBizWidgetDatasource() {
        bizWidgetDatasourceExtHelper.registerDatasource(BizWidgetDatasourceType.UPMS_USER_TYPE, this);
    }

    @Override
    public MyPageData<Map<String, Object>> getDataList(Map<String, Object> filter, MyOrderParam orderParam,
                                                       MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        String orderBy = orderParam == null ? null : MyOrderParam.buildOrderBy(orderParam, SysUser.class);
        SysUser userFilter = filter == null ? null : BeanUtil.toBean(filter, SysUser.class);
        List<SysUser> userList = this.getSysUserList(userFilter, orderBy);
        this.buildRelationForDataList(userList, MyRelationParam.dictOnly());
        return MyPageUtil.makeResponseData(userList, BeanUtil::beanToMap);
    }

    @Override
    public List<Map<String, Object>> getDataListByIds(List<String> ids) {
        List<SysUser> userList = this.getInList(ids.stream().map(Long::valueOf).collect(Collectors.toSet()));
        this.buildRelationForDataList(userList, MyRelationParam.dictOnly());
        return MyModelUtil.beanToMapList(userList);
    }

    /**
     * 获取指定登录名的用户对象。
     *
     * @param loginName 指定登录用户名。
     * @return 用户对象。
     */
    @Override
    public SysUser getSysUserByLoginName(String loginName) {
        SysUser filter = new SysUser();
        filter.setLoginName(loginName);
        return sysUserMapper.selectOne(new QueryWrapper<>(filter));
    }

    /**
     * 保存新增的用户对象。
     *
     * @param user          新增的用户对象。
     * @param roleIdSet     用户角色Id集合。
     * @param deptPostIdSet 部门岗位Id集合。
     * @param dataPermIdSet 数据权限Id集合。
     * @return 新增后的用户对象。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysUser saveNew(SysUser user, Set<Long> roleIdSet, Set<Long> deptPostIdSet, Set<Long> dataPermIdSet,
                           Set<Long> projectIdSet) {
        user.setUserId(idGenerator.nextLongId());
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setUserStatus(SysUserStatus.STATUS_NORMAL);
        user.setDeletedFlag(GlobalDeletedFlag.NORMAL);
        MyModelUtil.fillCommonsForInsert(user);
        sysUserMapper.insert(user);
        if (CollUtil.isNotEmpty(deptPostIdSet)) {
            for (Long deptPostId : deptPostIdSet) {
                SysDeptPost deptPost = sysDeptService.getSysDeptPost(deptPostId);
                SysUserPost userPost = new SysUserPost();
                userPost.setUserId(user.getUserId());
                userPost.setDeptPostId(deptPostId);
                userPost.setPostId(deptPost.getPostId());
                sysUserPostMapper.insert(userPost);
            }
        }
        if (CollUtil.isNotEmpty(roleIdSet)) {
            for (Long roleId : roleIdSet) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(user.getUserId());
                userRole.setRoleId(roleId);
                sysUserRoleMapper.insert(userRole);
            }
        }
        if (CollUtil.isNotEmpty(dataPermIdSet)) {
            for (Long dataPermId : dataPermIdSet) {
                SysDataPermUser dataPermUser = new SysDataPermUser();
                dataPermUser.setDataPermId(dataPermId);
                dataPermUser.setUserId(user.getUserId());
                sysDataPermUserMapper.insert(dataPermUser);
            }
        }
        return user;
    }

    /**
     * 更新用户对象。
     *
     * @param user          更新的用户对象。
     * @param originalUser  原有的用户对象。
     * @param roleIdSet     用户角色Id列表。
     * @param deptPostIdSet 部门岗位Id集合。
     * @param dataPermIdSet 数据权限Id集合。
     * @return 更新成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SysUser user, SysUser originalUser, Set<Long> roleIdSet, Set<Long> deptPostIdSet,
                          Set<Long> dataPermIdSet, Set<Long> projectIdSet) {
        user.setLoginName(originalUser.getLoginName());
        user.setPassword(originalUser.getPassword());
        user.setOneCardNo(originalUser.getOneCardNo());
        user.setUserTags(originalUser.getUserTags());
        user.setFacePicture(originalUser.getFacePicture());
        MyModelUtil.fillCommonsForUpdate(user, originalUser);
        UpdateWrapper<SysUser> uw = this.createUpdateQueryForNullValue(user, user.getUserId());
        if (sysUserMapper.update(user, uw) != 1) {
            return false;
        }
        // 先删除原有的User-Post关联关系，再重新插入新的关联关系
        SysUserPost deletedUserPost = new SysUserPost();
        deletedUserPost.setUserId(user.getUserId());
        sysUserPostMapper.delete(new QueryWrapper<>(deletedUserPost));
        if (CollUtil.isNotEmpty(deptPostIdSet)) {
            for (Long deptPostId : deptPostIdSet) {
                SysDeptPost deptPost = sysDeptService.getSysDeptPost(deptPostId);
                SysUserPost userPost = new SysUserPost();
                userPost.setUserId(user.getUserId());
                userPost.setDeptPostId(deptPostId);
                userPost.setPostId(deptPost.getPostId());
                sysUserPostMapper.insert(userPost);
            }
        }
        // 先删除原有的User-Role关联关系，再重新插入新的关联关系
        SysUserRole deletedUserRole = new SysUserRole();
        deletedUserRole.setUserId(user.getUserId());
        sysUserRoleMapper.delete(new QueryWrapper<>(deletedUserRole));
        if (CollUtil.isNotEmpty(roleIdSet)) {
            for (Long roleId : roleIdSet) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(user.getUserId());
                userRole.setRoleId(roleId);
                sysUserRoleMapper.insert(userRole);
            }
        }
        // 先删除原有的DataPerm-User关联关系，在重新插入新的关联关系
        SysDataPermUser deletedDataPermUser = new SysDataPermUser();
        deletedDataPermUser.setUserId(user.getUserId());
        sysDataPermUserMapper.delete(new QueryWrapper<>(deletedDataPermUser));
        if (CollUtil.isNotEmpty(dataPermIdSet)) {
            for (Long dataPermId : dataPermIdSet) {
                SysDataPermUser dataPermUser = new SysDataPermUser();
                dataPermUser.setDataPermId(dataPermId);
                dataPermUser.setUserId(user.getUserId());
                sysDataPermUserMapper.insert(dataPermUser);
            }
        }
        return true;
    }

    /**
     * 修改用户密码。
     *
     * @param userId  用户主键Id。
     * @param newPass 新密码。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean changePassword(Long userId, String newPass) {
        SysUser updatedUser = new SysUser();
        updatedUser.setUserId(userId);
        updatedUser.setPassword(passwordEncoder.encode(newPass));
        return sysUserMapper.updateById(updatedUser) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean changeHeadImage(Long userId, String newHeadImage) {
        SysUser updatedUser = new SysUser();
        updatedUser.setUserId(userId);
        updatedUser.setHeadImageUrl(newHeadImage);
        TokenData.takeFromRequest().setHeadImageUrl(newHeadImage);
        putTokenDataToSessionCache(TokenData.takeFromRequest());
        return sysUserMapper.updateById(updatedUser) == 1;
    }

    @Override
    public ResponseResult<Void> changeShowName(String showName) {
        SysUser updatedUser = new SysUser();
        updatedUser.setUserId(TokenData.takeFromRequest().getUserId());
        updatedUser.setShowName(showName);
        sysUserMapper.updateById(updatedUser);
        TokenData.takeFromRequest().setShowName(showName);
        putTokenDataToSessionCache(TokenData.takeFromRequest());
        return ResponseResult.success();
    }

    /**
     * 删除指定数据。
     *
     * @param userId 主键Id。
     * @return 成功返回true，否则false。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long userId) {
        // 删除用户
        if (sysUserMapper.deleteById(userId) == 0) {
            return false;
        }
        // 删除用户角色
        SysUserRole userRole = new SysUserRole();
        userRole.setUserId(userId);
        sysUserRoleMapper.delete(new QueryWrapper<>(userRole));

        // 删除用户岗位
        SysUserPost userPost = new SysUserPost();
        userPost.setUserId(userId);
        sysUserPostMapper.delete(new QueryWrapper<>(userPost));
        return true;
    }

    /**
     * 获取单表查询结果。由于没有关联数据查询，因此在仅仅获取单表数据的场景下，效率更高。 如果需要同时获取关联数据，请移步(getSysUserListWithRelation)方法。
     *
     * @param filter  过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<SysUser> getSysUserList(SysUser filter, String orderBy) {
        return sysUserMapper.getSysUserList(filter, orderBy);
    }

    /**
     * 获取主表的查询结果，以及主表关联的字典数据和一对一从表数据，以及一对一从表的字典数据。 该查询会涉及到一对一从表的关联过滤，或一对多从表的嵌套关联过滤，因此性能不如单表过滤。
     * 如果仅仅需要获取主表数据，请移步(getSysUserList)，以便获取更好的查询性能。
     *
     * @param filter  主表过滤对象。
     * @param orderBy 排序参数。
     * @return 查询结果集。
     */
    @Override
    public List<SysUser> getSysUserListWithRelation(SysUser filter, String orderBy) {
        List<SysUser> resultList = sysUserMapper.getSysUserList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    /**
     * 获取指定角色的用户列表。
     *
     * @param roleId  角色主键Id。
     * @param filter  用户过滤对象。
     * @param orderBy 排序参数。
     * @return 用户列表。
     */
    @Override
    public List<SysUser> getSysUserListByRoleId(Long roleId, SysUser filter, String orderBy) {
        return sysUserMapper.getSysUserListByRoleId(roleId, filter, orderBy);
    }

    /**
     * 获取不属于指定角色的用户列表。
     *
     * @param roleId  角色主键Id。
     * @param filter  用户过滤对象。
     * @param orderBy 排序参数。
     * @return 用户列表。
     */
    @Override
    public List<SysUser> getNotInSysUserListByRoleId(Long roleId, SysUser filter, String orderBy) {
        return sysUserMapper.getNotInSysUserListByRoleId(roleId, filter, orderBy);
    }

    /**
     * 获取指定数据权限的用户列表。
     *
     * @param dataPermId 数据权限主键Id。
     * @param filter     用户过滤对象。
     * @param orderBy    排序参数。
     * @return 用户列表。
     */
    @Override
    public List<SysUser> getSysUserListByDataPermId(Long dataPermId, SysUser filter, String orderBy) {
        return sysUserMapper.getSysUserListByDataPermId(dataPermId, filter, orderBy);
    }

    /**
     * 获取不属于指定数据权限的用户列表。
     *
     * @param dataPermId 数据权限主键Id。
     * @param filter     用户过滤对象。
     * @param orderBy    排序参数。
     * @return 用户列表。
     */
    @Override
    public List<SysUser> getNotInSysUserListByDataPermId(Long dataPermId, SysUser filter, String orderBy) {
        return sysUserMapper.getNotInSysUserListByDataPermId(dataPermId, filter, orderBy);
    }

    @Override
    public List<SysUser> getSysUserListByDeptPostId(Long deptPostId, SysUser filter, String orderBy) {
        return sysUserMapper.getSysUserListByDeptPostId(deptPostId, filter, orderBy);
    }

    @Override
    public List<SysUser> getNotInSysUserListByDeptPostId(Long deptPostId, SysUser filter, String orderBy) {
        return sysUserMapper.getNotInSysUserListByDeptPostId(deptPostId, filter, orderBy);
    }

    @Override
    public List<SysUser> getSysUserListByPostId(Long postId, SysUser filter, String orderBy) {
        return sysUserMapper.getSysUserListByPostId(postId, filter, orderBy);
    }

    /**
     * 查询用户的权限资源地址列表。同时返回详细的分配路径。
     *
     * @param userId 用户Id。
     * @param url    url过滤条件。
     * @return 包含从用户到权限资源的完整权限分配路径信息的查询结果列表。
     */
    @Override
    public List<Map<String, Object>> getSysPermListWithDetail(Long userId, String url) {
        return sysUserMapper.getSysPermListWithDetail(userId, url);
    }

    /**
     * 查询用户的权限字列表。同时返回详细的分配路径。
     *
     * @param userId   用户Id。
     * @param permCode 权限字名称过滤条件。
     * @return 包含从用户到权限字的权限分配路径信息的查询结果列表。
     */
    @Override
    public List<Map<String, Object>> getSysPermCodeListWithDetail(Long userId, String permCode) {
        return sysUserMapper.getSysPermCodeListWithDetail(userId, permCode);
    }

    /**
     * 查询用户的菜单列表。同时返回详细的分配路径。
     *
     * @param userId   用户Id。
     * @param menuName 菜单名称过滤条件。
     * @return 包含从用户到菜单的权限分配路径信息的查询结果列表。
     */
    @Override
    public List<Map<String, Object>> getSysMenuListWithDetail(Long userId, String menuName) {
        return sysUserMapper.getSysMenuListWithDetail(userId, menuName);
    }

    /**
     * 验证用户对象关联的数据是否都合法。
     *
     * @param sysUser         当前操作的对象。
     * @param originalSysUser 原有对象。
     * @param roleIds         逗号分隔的角色Id列表字符串。
     * @param deptPostIds     逗号分隔的部门岗位Id列表字符串。
     * @param dataPermIds     逗号分隔的数据权限Id列表字符串。
     * @return 验证结果。
     */
    @Override
    public CallResult verifyRelatedData(SysUser sysUser, SysUser originalSysUser, String roleIds, String deptPostIds,
                                        String dataPermIds, String projectIds) {
        JSONObject jsonObject = new JSONObject();
        /* if (StrUtil.isBlank(deptPostIds)) {
            return CallResult.error("数据验证失败，用户的部门岗位数据不能为空！");
        }*/
        Set<Long> deptPostIdSet = null;
        if (StrUtil.isNotBlank(deptPostIds)) {
            deptPostIdSet = Arrays.stream(deptPostIds.split(",")).map(Long::valueOf).collect(Collectors.toSet());
        }
        /*if (!sysPostService.existAllPrimaryKeys(deptPostIdSet, sysUser.getDeptId())) {
            return CallResult.error("数据验证失败，存在不合法的用户岗位，请刷新后重试！");
        }*/
        jsonObject.put("deptPostIdSet", deptPostIdSet);
        if (StrUtil.isBlank(roleIds)) {
            return CallResult.error("数据验证失败，用户的角色数据不能为空！");
        }
        Set<Long> roleIdSet = Arrays.stream(roleIds.split(",")).map(Long::valueOf).collect(Collectors.toSet());
        if (!sysRoleService.existAllPrimaryKeys(roleIdSet)) {
            return CallResult.error("数据验证失败，存在不合法的用户角色，请刷新后重试！");
        }
        jsonObject.put("roleIdSet", roleIdSet);
        if (StrUtil.isBlank(dataPermIds)) {
            return CallResult.error("数据验证失败，用户的数据权限不能为空！");
        }
        Set<Long> dataPermIdSet = Arrays.stream(dataPermIds.split(",")).map(Long::valueOf).collect(Collectors.toSet());
        if (!sysDataPermService.existAllPrimaryKeys(dataPermIdSet)) {
            return CallResult.error("数据验证失败，存在不合法的数据权限，请刷新后重试！");
        }
        jsonObject.put("dataPermIdSet", dataPermIdSet);
        if (StrUtil.isBlank(projectIds)) {
            return CallResult.error("数据验证失败，用户的项目不能为空！");
        }
        Set<Long> projectIdSet = Arrays.stream(projectIds.split(",")).map(Long::valueOf).collect(Collectors.toSet());
        jsonObject.put("projectIdSet", projectIdSet);
        // 这里是基于字典的验证。
        if (this.needToVerify(sysUser, originalSysUser, SysUser::getDeptId)
                && !sysDeptService.existId(sysUser.getDeptId())) {
            return CallResult.error("数据验证失败，关联的用户部门Id并不存在，请刷新后重试！");
        }
        return CallResult.ok(jsonObject);
    }

    @Override
    public SysUser getSysUserByOaUserId(String oaType, String oaUserId) {
        if (StringUtils.isBlank(oaUserId)) {
            throw new ServiceException("当前用户没有应用权限");
        }
        if (StringUtils.isBlank(oaType)) {
            oaType = OaTypeEnums.DING_TALK.name();
        }
        return sysUserMapper.selectSysUserByOaUserId(oaType, oaUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(SysUserSaveOrUpdateDTO sysUserSaveOrUpdateDTO) {
        // String password = sysUserSaveOrUpdateDTO.getPassword();
        // 取消密码
        // String confirmPassword = sysUserSaveOrUpdateDTO.getConfirmPassword();

        Long userId = sysUserSaveOrUpdateDTO.getUserId();
        String loginName = sysUserSaveOrUpdateDTO.getLoginName();
        String showName = sysUserSaveOrUpdateDTO.getShowName();
        Integer sex = sysUserSaveOrUpdateDTO.getSex();
        String phone = sysUserSaveOrUpdateDTO.getPhone();
        String shortPhone = sysUserSaveOrUpdateDTO.getShortPhone();
        String cardNo = sysUserSaveOrUpdateDTO.getCardNo();

        // Long deptId = sysUserSaveOrUpdateDTO.getDeptId();

        if (StringUtils.isEmpty(phone) || phone.contains("*")) {
            phone = null;
        }
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginName(loginName);
        sysUser.setShowName(showName);
        sysUser.setSex(sex);
        sysUser.setPhone(phone);
        sysUser.setPhotoUrl(sysUserSaveOrUpdateDTO.getPhotoUrl());
        sysUser.setEmployeeNumber(sysUserSaveOrUpdateDTO.getEmployeeNumber());
        sysUser.setExtensionNumber(sysUserSaveOrUpdateDTO.getExtensionNumber());
        sysUser.setCardNo(cardNo);
        // sysUser.setDeptId(deptId);
        sysUser.setShortPhone(shortPhone);
        sysUser.setDeviceSn(sysUserSaveOrUpdateDTO.getDeviceSn());

        // 查询手机号是否存在
        Long phoneCount = sysUserMapper.selectCount(Wrappers.lambdaQuery(SysUser.class).eq(SysUser::getPhone, phone)
                .ne(userId != null, SysUser::getUserId, userId));
        if (phoneCount > 0) {
            throw new RuntimeException("用户创建失败，手机号已存在！");
        }

        // 查询手机短号是否存在
        if (StringUtils.isNotBlank(shortPhone)) {
            Long shortphoneCount = sysUserMapper.selectCount(Wrappers.lambdaQuery(SysUser.class)
                    .eq(SysUser::getShortPhone, shortPhone).ne(userId != null, SysUser::getUserId, userId));
            if (shortphoneCount > 0) {
                throw new RuntimeException("用户创建失败，短号已存在！");
            }
        }

        // 查询用户身份证号是否存在
        Long cardNoCount = sysUserMapper.selectCount(Wrappers.lambdaQuery(SysUser.class).eq(SysUser::getCardNo, cardNo)
                .ne(userId != null, SysUser::getUserId, userId));
        if (cardNoCount > 0) {
            throw new RuntimeException("用户创建失败，身份证号已存在！");
        }

        // 查询用户账号
        Long loginNameCount = sysUserMapper.selectCount(Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getLoginName, loginName).ne(userId != null, SysUser::getUserId, userId));
        if (loginNameCount > 0) {
            throw new RuntimeException("用户创建失败，账号已存在！");
        }

        if (userId == null) {
            // if (!Objects.equals(password, confirmPassword)) {
            // throw new MyRuntimeException("密码与确认密码不一致，请检查后重新提交！");
            // }
            // 获取默认密码
            SysConfig sysConfig = sysConfigMapper.selectById(DEFAULT_PASSWORD);
            if (null == sysConfig) {
                throw new RuntimeException("没有配置默认密码，请核对。");
            }
            sysUser.setPassword(passwordEncoder.encode(sysConfig.getKeyValue()));
            sysUser.setUserType(1);
            sysUser.setUserStatus(SysUserStatus.STATUS_NORMAL);
            sysUser.setDeletedFlag(GlobalDeletedFlag.NORMAL);
            sysUser.setCreateUserId(TokenData.takeFromRequest().getUserId());
            sysUser.setCreateTime(new Date());
            sysUserMapper.insert(sysUser);
        } else {
            sysUser.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            sysUser.setUpdateTime(new Date());
            sysUserMapper.updateById(sysUser);

            // 删除之前关联的角色、岗位、标签
            sysUserRoleMapper
                    .delete(Wrappers.lambdaQuery(SysUserRole.class).eq(SysUserRole::getUserId, sysUser.getUserId()));
            sysUserPostMapper
                    .delete(Wrappers.lambdaQuery(SysUserPost.class).eq(SysUserPost::getUserId, sysUser.getUserId()));
            sysUserTagMapper
                    .delete(Wrappers.lambdaQuery(SysUserTag.class).eq(SysUserTag::getUserId, sysUser.getUserId()));
        }

        // 用户部门
        List<Long> deptIds = sysUserSaveOrUpdateDTO.getDeptId();

        if (CollectionUtil.isNotEmpty(deptIds)) {
            // 删除不存在部门
            new LambdaUpdateChainWrapper<>(sysUserDeptMapper).eq(SysUserDept::getUserId, sysUser.getUserId())
                    .notIn(SysUserDept::getDeptId, deptIds).remove();
            List<SysUserDept> sysUserDepts = sysUserDeptMapper.selectList(SysUserDept::getUserId, sysUser.getUserId());
            Set<Long> inDepts = new HashSet<>();
            if (CollectionUtil.isNotEmpty(sysUserDepts)) {
                inDepts = sysUserDepts.stream().map(SysUserDept::getDeptId).collect(Collectors.toSet());
            }
            for (Long deptIdL : deptIds) {
                if (!inDepts.contains(deptIdL)) {
                    SysUserDept sysUserDept = new SysUserDept(sysUser.getUserId(), deptIdL, 0);
                    sysUserDeptMapper.insert(sysUserDept);
                }
            }
        }

        // 用户角色
        List<SysUserRole> sysUserRoles = new ArrayList<>();
        List<Long> roleIds = sysUserSaveOrUpdateDTO.getRoleIds();
        if (CollectionUtil.isNotEmpty(roleIds)) {
            for (Long roleId : roleIds) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUser.getUserId());
                sysUserRole.setRoleId(roleId);
                sysUserRoles.add(sysUserRole);
            }
        }
        if (CollectionUtil.isNotEmpty(sysUserRoles)) {
            sysUserRoleMapper.batchInsert(sysUserRoles);
        }

        // 用户岗位
        List<SysUserPost> sysUserPosts = new ArrayList<>();
        List<Long> deptPostIds = sysUserSaveOrUpdateDTO.getDeptPostIds();
        if (CollectionUtil.isNotEmpty(deptPostIds)) {
            List<SysDeptPost> sysDeptPosts = sysDeptPostMapper.selectBatchIds(deptPostIds);
            for (SysDeptPost sysDeptPost : sysDeptPosts) {
                SysUserPost sysUserPost = new SysUserPost();
                sysUserPost.setUserId(sysUser.getUserId());
                sysUserPost.setDeptPostId(sysDeptPost.getDeptPostId());
                sysUserPost.setPostId(sysDeptPost.getPostId());
                sysUserPosts.add(sysUserPost);
            }
        }
        if (CollectionUtil.isNotEmpty(sysUserPosts)) {
            // 删除原先的岗位
            new LambdaUpdateChainWrapper<>(sysUserPostMapper).eq(SysUserPost::getUserId, sysUser.getUserId()).remove();
            sysUserPostMapper.insertBatch(sysUserPosts);
        }

        // 用户标签
        List<SysUserTag> sysUserTags = new ArrayList<>();
        List<Long> tagIds = sysUserSaveOrUpdateDTO.getTagIds();
        if (CollectionUtil.isNotEmpty(tagIds)) {
            for (Long tagId : tagIds) {
                SysUserTag sysUserTag = new SysUserTag();
                sysUserTag.setUserId(sysUser.getUserId());
                sysUserTag.setTagId(tagId);
                sysUserTags.add(sysUserTag);
            }
        }
        if (CollectionUtil.isNotEmpty(sysUserTags)) {
            sysUserTagMapper.insertBatch(sysUserTags);
        }

        return sysUser.getUserId();
    }

    @Override
    public void updateImg(SysUserSaveOrUpdateDTO sysUserSaveOrUpdateDTO) {
        SysUser sysUser = MyModelUtil.copyTo(sysUserSaveOrUpdateDTO, SysUser.class);
        sysUserMapper.updateById(sysUser);
        TokenData.takeFromRequest().setFacePicture(sysUserSaveOrUpdateDTO.getFacePicture());
        putTokenDataToSessionCache(TokenData.takeFromRequest());
    }

    @Override
    public MyPageData<SysUserListVO> list(SysUserQueryDTO sysUserQueryDTO) {
        Integer pageNum = sysUserQueryDTO.getPageNum();
        Integer pageSize = sysUserQueryDTO.getPageSize();
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }

        // 查询用户列表
        List<SysUserListVO> sysUserListVOS = sysUserMapper.queryList(sysUserQueryDTO);
        sysUserListVOS.forEach(x -> x.setPhone(sysUserQueryDTO.getIsShowPhone() == 0 ? "" : x.getPhone()));
        return MyPageUtil.makeResponseData(sysUserListVOS);
    }

    @Override
    public void updateStatus(Long userId) {
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser != null) {
            Integer userStatus = sysUser.getUserStatus();
            // 状态(0: 正常 1: 锁定)
            userStatus = Objects.equals(userStatus, 0) ? 1 : 0;
            SysUser updateUser = new SysUser();
            updateUser.setUserId(userId);
            updateUser.setUserStatus(userStatus);
            sysUserMapper.updateById(updateUser);
        }
    }

    public static String sensitiveMobile(String mobile) {
        if (StringUtils.isEmpty(mobile))
            return mobile;
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    @Override
    public SysUserDetailVO detail(Long userId) {
        // 查询用户详情
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser == null) {
            return null;
        }

        SysUserDetailVO sysUserDetailVO = new SysUserDetailVO();
        sysUserDetailVO.setUserId(sysUser.getUserId());
        sysUserDetailVO.setLoginName(sysUser.getLoginName());
        sysUserDetailVO.setShowName(sysUser.getShowName());
        sysUserDetailVO.setPhone(sensitiveMobile(sysUser.getPhone()));
        sysUserDetailVO.setCardNo(sysUser.getCardNo());
        sysUserDetailVO.setSex(sysUser.getSex());
        sysUserDetailVO.setUserStatus(sysUser.getUserStatus());
        sysUserDetailVO.setUserType(sysUser.getUserType());
        sysUserDetailVO.setCreateTime(sysUser.getCreateTime());
        sysUserDetailVO.setLastLoginTime(sysUser.getLastLoginTime());
        sysUserDetailVO.setShortPhone(sysUser.getShortPhone());
        sysUserDetailVO.setPhotoUrl(sysUser.getPhotoUrl());
        sysUserDetailVO.setEmployeeNumber(sysUser.getEmployeeNumber());
        sysUserDetailVO.setExtensionNumber(sysUser.getExtensionNumber());
        sysUserDetailVO.setDeviceSn(sysUser.getDeviceSn());

        // 查询部门信息
        List<SysUserDept> sysUserDepts = sysUserDeptMapper.selectList(SysUserDept::getUserId, sysUser.getUserId());
        if (CollectionUtil.isNotEmpty(sysUserDepts)) {
            List<SysDept> sysDepts = sysDeptMapper
                    .selectBatchIds(sysUserDepts.stream().map(SysUserDept::getDeptId).collect(Collectors.toList()));
            sysUserDetailVO.setSysDeptVOs(SysDept.INSTANCE.fromModelList(sysDepts));
        }

        // 查询角色信息
        List<SysUserRole> sysUserRoles =
                sysUserRoleMapper.selectList(Wrappers.lambdaQuery(SysUserRole.class).eq(SysUserRole::getUserId, userId));
        if (CollectionUtil.isNotEmpty(sysUserRoles)) {
            Set<Long> roleIds = sysUserRoles.stream().map(SysUserRole::getRoleId).collect(Collectors.toSet());
            List<SysRole> sysRoles = sysRoleService.listByIds(roleIds);
            sysUserDetailVO.setSysRoles(sysRoles);
        }

        // 查询岗位
        List<SysUserPost> sysUserPosts =
                sysUserPostMapper.selectList(Wrappers.lambdaQuery(SysUserPost.class).eq(SysUserPost::getUserId, userId));
        if (CollectionUtil.isNotEmpty(sysUserPosts)) {
            Set<Long> deptPostIds = sysUserPosts.stream().map(SysUserPost::getDeptPostId).collect(Collectors.toSet());
            List<SysDeptPost> sysDeptPosts = sysDeptPostMapper.selectBatchIds(deptPostIds);
            sysUserDetailVO.setSysDeptPosts(sysDeptPosts);
        }

        // 查询用户标签
        List<SysUserTag> sysUserTags =
                sysUserTagMapper.selectList(Wrappers.lambdaQuery(SysUserTag.class).eq(SysUserTag::getUserId, userId));
        if (CollectionUtil.isNotEmpty(sysUserTags)) {
            Set<Long> tagIds = sysUserTags.stream().map(SysUserTag::getTagId).collect(Collectors.toSet());
            List<SysTag> sysTags = sysTagMapper.selectBatchIds(tagIds);
            sysUserDetailVO.setSysTags(sysTags);
        }

        // 注意：用户对应的权限组在 sub 模块，无法直接访问，需要调用权限组接口查询用户对应的权限组列表
        return sysUserDetailVO;
    }

    private void putTokenDataToSessionCache(TokenData tokenData) {
        String sessionIdKey = RedisKeyUtil.makeSessionIdKey(tokenData.getSessionId());
        String sessionData = JSON.toJSONString(tokenData, SerializerFeature.WriteNonStringValueAsString);
        RBucket<String> bucket = redissonClient.getBucket(sessionIdKey);
        bucket.set(sessionData);
        bucket.expire(appConfig.getSessionExpiredSeconds(), TimeUnit.SECONDS);
    }

    // TODO 业务中去处理图片上传到海康图片
    // @Override
    // public void uploadFacePicture(UploadFacePictureDTO params) {
    //
    // List<SysUserProject> userProjectList = sysUserProjectMapper.selectList(new LambdaQueryWrapper<SysUserProject>()
    // .eq(SysUserProject::getUserId, params.getUserId()));
    // for (SysUserProject sysUserProject : userProjectList) {
    // List<SubSystemConfig> subSystemConfigs = subSystemConfigMapper.selectList(new
    // QueryWrapper<SubSystemConfig>().lambda()
    // .eq(SubSystemConfig::getFactory, SubSystemFactoryEnums.HIK_GUARD.name()));
    // if(CollectionUtils.isNotEmpty(subSystemConfigs)) {
    // // 上传人脸照
    // hikVisionEntranceGuardProvider.pushFacePicture(subSystemConfigs.get(0), params.getUserId(),
    // params.getFacePicture());
    // }
    // }
    // SysUser sysUser = new SysUser();
    // sysUser.setUserId(params.getUserId());
    // sysUser.setFacePicture(params.getFacePicture());
    // sysUserMapper.updateById(sysUser);
    // }

    @Override
    public MyPageData<SysUserListVO> listByDeptId(SysUserQueryDTO sysUserQueryDTO) {
        Integer pageNum = sysUserQueryDTO.getPageNum();
        Integer pageSize = sysUserQueryDTO.getPageSize();
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        // 查询部门下的人员
        List<SysUserListVO> sysUserList = sysUserMapper.listByDeptId(sysUserQueryDTO);
        return MyPageUtil.makeResponseData(sysUserList);
    }

    @Override
    public void flipManager(Long userId, Long deptId) {
        SysUserDept one = new LambdaQueryChainWrapper<>(sysUserDeptMapper).eq(SysUserDept::getDeptId, deptId)
                .eq(SysUserDept::getUserId, userId).last("limit 1").one();
        if (one != null) {
            new LambdaUpdateChainWrapper<>(sysUserDeptMapper).eq(SysUserDept::getDeptId, deptId)
                    .eq(SysUserDept::getUserId, userId).set(SysUserDept::getLeader, one.getLeader() == 0 ? 1 : 0).update();
        }
    }

    @Override
    public ResponseResult<JSONObject> doLogin(String loginName, String password) throws Exception {
        String errorMessage;
        SysConfig sysConfig = sysConfigMapper.selectById(DEFAULT_PASSWORD);
        if (null == sysConfig) {
            errorMessage = "没有配置默认密码，请核对。";
            return ResponseResult.error(ErrorCodeEnum.NO_DEFAULT_PASSWORD_CONFIG, errorMessage);
        }

        boolean resetPwd = true;
        SysConfig resetPwdConfig = sysConfigMapper.selectById(RESET_PASSWORD);
        if (resetPwdConfig != null) {
            String keyValue = resetPwdConfig.getKeyValue();
            if (Objects.equals("0", keyValue)) {
                resetPwd = false;
            }
        }

        String passwordStr;
        passwordStr = URLDecoder.decode(password, StandardCharsets.UTF_8.name());
        passwordStr = RsaUtil.decrypt(passwordStr, ApplicationConstant.PRIVATE_KEY);
        // 是否需要重置默认密码
        Boolean isResetPassword = false;
        if (passwordStr.equals(sysConfig.getKeyValue())) {
            isResetPassword = true;
        }
        ;
        // 限制5秒钟之内最多访问10次
        String ip = IpUtil.getRemoteIpAddress(ContextUtil.getHttpRequest());
        RBucket<String> loginBucket = redissonClient.getBucket("LOGIN_REQUEST:" + ip);
        String oriCount = loginBucket.get();
        if (StringUtils.isEmpty(oriCount)) {
            loginBucket.set("1", 5L, TimeUnit.SECONDS);
        } else {
            int requestCount = Integer.parseInt(oriCount);
            if (requestCount >= 10) {
                errorMessage = "接口请求频率过快，请稍后访问。";
                return ResponseResult.error(ErrorCodeEnum.NO_ACCESS_PERMISSION, errorMessage);
            }
            loginBucket.set(String.valueOf(++requestCount), 5L, TimeUnit.SECONDS);
        }

        // 限制20分钟之内用户名密码错误不能超过5次
        RBucket<String> errorBucket = redissonClient.getBucket("PASSWORD_ERR:" + ip);
        String oriErrorCount = errorBucket.get();
        int errorCount = 0;
        if (StringUtils.isNotEmpty(oriErrorCount)) {
            errorCount = Integer.parseInt(oriErrorCount);
            if (errorCount >= 5) {
                errorMessage = "用户名或密码错误达到5次，请20分钟之后再试！";
                return ResponseResult.error(ErrorCodeEnum.INVALID_USERNAME_PASSWORD, errorMessage);
            }
        }
        SysUser filter = new SysUser();
        filter.setLoginName(loginName);
        SysUser user = sysUserMapper.selectOne(new QueryWrapper<>(filter));
        password = URLDecoder.decode(password, StandardCharsets.UTF_8.name());
        password = RsaUtil.decrypt(password, ApplicationConstant.PRIVATE_KEY);
        if (user == null || !passwordEncoder.matches(password, user.getPassword())) {
            return ResponseResult.error(ErrorCodeEnum.INVALID_USERNAME_PASSWORD);
        }
        if (user.getUserStatus() == SysUserStatus.STATUS_LOCKED) {
            errorMessage = "登录失败，用户账号被锁定！";
            return ResponseResult.error(ErrorCodeEnum.INVALID_USER_STATUS, errorMessage);
        }
        JSONObject jsonObject = this.buildLoginData(user);
        // 密码强度校验
        boolean isStrongPassword = CheckPwdUtil.isStrongPassword(password);
        JSONObject jsonData = new JSONObject();
        jsonData.put("isStrongPassword", resetPwd ? isStrongPassword : true);
        jsonData.put("isResetPassword", resetPwd ? isResetPassword : false);
        jsonData.put("data", jsonObject.getString(TokenData.REQUEST_ATTRIBUTE_NAME));
        return ResponseResult.success(jsonData);
    }

    private JSONObject buildLoginData(SysUser user) {
        int deviceType = MyCommonUtil.getDeviceType();
        boolean isAdmin = user.getUserType() == SysUserType.TYPE_ADMIN;
        Map<String, Object> claims = new HashMap<>(3);
        String sessionId = user.getLoginName() + "_" + deviceType + "_" + MyCommonUtil.generateUuid();
        claims.put("sessionId", sessionId);
        String uaStr = httpServletRequest.getHeader("user-agent");
        UserAgent ua = UserAgentUtil.parse(uaStr);
        Long expiration = appConfig.getExpiration();
        if (ua.isMobile()) {
            expiration = appConfig.getAppExpiration();
        }
        String token = JwtUtil.generateToken(claims, expiration, appConfig.getTokenSigningKey());
        JSONObject jsonData = new JSONObject();
        jsonData.put(TokenData.REQUEST_ATTRIBUTE_NAME, token);
        jsonData.put("showName", user.getShowName());
        jsonData.put("isAdmin", isAdmin);
        // 工号 特殊业务时使用
        jsonData.put("jobNumber", user.getLoginName());
        jsonData.put("shortPhone", user.getShortPhone());

        if (StrUtil.isNotBlank(user.getHeadImageUrl())) {
            jsonData.put("headImageUrl", user.getHeadImageUrl());
        }
        TokenData tokenData = this.buildTokenData(user, sessionId, deviceType);
        // jsonData.put("deptName", tokenData.getDeptName());
        this.putTokenDataToSessionCache(tokenData);
        // 这里手动将TokenData存入request，便于OperationLogAspect统一处理操作日志。
        TokenData.addToRequest(tokenData);
        Collection<SysMenu> allMenuList;
        Collection<String> permCodeList;
        if (isAdmin) {
            allMenuList = sysMenuService.getAllListByOrder("showOrder");
            permCodeList = sysPermCodeService.getAllPermCodeList();
        } else {
            allMenuList = sysMenuService.getMenuListByUserId(user.getUserId(), null);
            permCodeList = sysPermCodeService.getPermCodeListByUserId(user.getUserId());
        }
        List<SysMenu> menuList =
                allMenuList.stream().filter(m -> m.getMenuType() <= SysMenuType.TYPE_MENU).collect(Collectors.toList());
        jsonData.put("menuList", menuList);
        jsonData.put("permCodeList", permCodeList);
        Set<String> permSet = null;
        if (!isAdmin) {
            // 所有登录用户都有白名单接口的访问权限。
            List<String> whitelist = this.getAndCacheWhitelist(sessionId);
            permSet = new HashSet<>(whitelist);
            if (StrUtil.isNotBlank(tokenData.getRoleIds())) {
                List<Long> roleIds =
                        StrUtil.split(tokenData.getRoleIds(), ',').stream().map(Long::valueOf).collect(Collectors.toList());
                Set<String> menuPermSet = this.getAndCacheMenuPermData(allMenuList, roleIds);
                permSet.addAll(menuPermSet);
            }
        }
        List<SysMenu> onlineMenuList = allMenuList.stream()
                .filter(m -> m.getOnlineFormId() != null && m.getMenuType().equals(SysMenuType.TYPE_BUTTON))
                .collect(Collectors.toList());
        OnlinePermData onlinePermData = this.getAndCacheOnlineMenuPermData(allMenuList, onlineMenuList);
        permCodeList.addAll(onlinePermData.permCodeSet);
        if (!isAdmin) {
            permSet.addAll(onlinePermData.permUrlSet);
            // 缓存用户的权限资源
            sysPermService.putUserSysPermCache(sessionId, user.getUserId(), permSet);
            sysDataPermService.putDataPermCache(sessionId, user.getUserId(), user.getDeptId());
            PermGroupInfo userPermGroup = sysPermCodeService.getUserPermGroup(user.getUserId());
            sysPermService.putPermGroupCache(sessionId, user.getUserId(), userPermGroup);
        }
        // 修改最后登录时间
        sysUserService.update(Wrappers.lambdaUpdate(new SysUser()).set(SysUser::getLastLoginTime, new Date())
                .eq(SysUser::getUserId, user.getUserId()));
        return jsonData;
    }

    private OnlinePermData getAndCacheOnlineMenuPermData(Collection<SysMenu> allMenuList,
                                                         List<SysMenu> onlineMenuList) {
        OnlinePermData permData = new OnlinePermData();
        if (CollUtil.isEmpty(onlineMenuList)) {
            return permData;
        }
        List<SysMenuPerm> allMenuPermList = MyModelUtil.copyCollectionTo(allMenuList, SysMenuPerm.class);
        List<SysMenuPerm> onlineMenuPermList =
                allMenuPermList.stream().filter(m -> m.getOnlineFormId() != null && m.getOnlineFlowEntryId() == null
                        && m.getMenuType().equals(SysMenuType.TYPE_MENU)).collect(Collectors.toList());
        Map<Long, List<SysMenuPerm>> onlineMenuPermMap =
                onlineMenuPermList.stream().collect(Collectors.groupingBy(SysMenuPerm::getOnlineFormId));
        this.buildOnlinePermData(onlineMenuList, onlineMenuPermMap, permData);
        // 这个非常非常重要，不能删除。因为在线票单的url前缀是可以配置的，那么表单字典接口的url也是动态。
        // 所以就不能把这个字典列表接口放到数据库的白名单表中。
        List<String> onlineWhitelistUrls =
                CollUtil.newArrayList(onlineProperties.getOperationUrlPrefix() + "/onlineOperation/listDict",
                        onlineApiProperties.getUrlPrefix() + "/onlineForm/render",
                        onlineApiProperties.getUrlPrefix() + "/onlineForm/view");
        String sessionId = TokenData.takeFromRequest().getSessionId();
        for (SysMenuPerm menuPerm : onlineMenuPermList) {
            menuPerm.getPermUrlSet().addAll(onlineWhitelistUrls);
            this.putMenuPermToCache(sessionId, menuPerm);
        }
        permData.permUrlSet.addAll(onlineWhitelistUrls);
        return permData;
    }

    static class OnlinePermData {
        public final Set<String> permCodeSet = new HashSet<>();
        public final Set<String> permUrlSet = new HashSet<>();
    }

    private void buildOnlinePermData(List<SysMenu> onlineMenuList, Map<Long, List<SysMenuPerm>> onlineMenuPermMap,
                                     OnlinePermData permData) {
        List<SysMenu> viewMenuList = onlineMenuList.stream()
                .filter(m -> m.getOnlineMenuPermType() == SysOnlineMenuPermType.TYPE_VIEW).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(viewMenuList)) {
            Set<Long> formIdSet = viewMenuList.stream().map(SysMenu::getOnlineFormId).collect(Collectors.toSet());
            List<OnlineDatasource> datasourceList = onlineDatasourceService.getOnlineDatasourceListByFormIds(formIdSet);
            for (OnlineDatasource datasource : datasourceList) {
                permData.permCodeSet.add(OnlineUtil.makeViewPermCode(datasource.getVariableName()));
                Set<String> permUrls = onlineProperties.getViewUrlList().stream()
                        .map(url -> url + datasource.getVariableName()).collect(Collectors.toSet());
                permData.permUrlSet.addAll(permUrls);
                List<SysMenuPerm> menuPermList =
                        onlineMenuPermMap.get(datasource.getOnlineFormDatasource().getFormId());
                for (SysMenuPerm menuPerm : menuPermList) {
                    menuPerm.getPermUrlSet().addAll(permUrls);
                }
            }
        }
        List<SysMenu> editMenuList = onlineMenuList.stream()
                .filter(m -> m.getOnlineMenuPermType() == SysOnlineMenuPermType.TYPE_EDIT).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(editMenuList)) {
            Set<Long> formIdSet = editMenuList.stream().map(SysMenu::getOnlineFormId).collect(Collectors.toSet());
            List<OnlineDatasource> datasourceList = onlineDatasourceService.getOnlineDatasourceListByFormIds(formIdSet);
            for (OnlineDatasource datasource : datasourceList) {
                permData.permCodeSet.add(OnlineUtil.makeEditPermCode(datasource.getVariableName()));
                Set<String> permUrls = onlineProperties.getEditUrlList().stream()
                        .map(url -> url + datasource.getVariableName()).collect(Collectors.toSet());
                permData.permUrlSet.addAll(permUrls);
                List<SysMenuPerm> menuPermList =
                        onlineMenuPermMap.get(datasource.getOnlineFormDatasource().getFormId());
                for (SysMenuPerm menuPerm : menuPermList) {
                    menuPerm.getPermUrlSet().addAll(permUrls);
                }
            }
        }
    }

    private List<String> getAndCacheWhitelist(String sessionId) {
        List<String> whitelistUrls = sysPermWhitelistService.getWhitelistPermList();
        if (dataFilterProperties.getEnabledDataPermFilter()) {
            // 白名单的数据缓存主要用户数据权限过滤是，如果菜单关联的权限中并不包含当前请求的url，则需要判断是否为白名单url。
            RBucket<String> cachedData = redissonClient.getBucket(RedisKeyUtil.makeSessionWhiteListPermKey(sessionId));
            cachedData.set(JSON.toJSONString(whitelistUrls));
            cachedData.expire(appConfig.getSessionExpiredSeconds(), TimeUnit.SECONDS);
        }
        return whitelistUrls;
    }

    private Set<String> getAndCacheMenuPermData(Collection<SysMenu> allMenuList, List<Long> roleIds) {
        List<SysMenuPerm> allMenuPermList = MyModelUtil.copyCollectionTo(allMenuList, SysMenuPerm.class);
        allMenuPermList = allMenuPermList.stream().filter(m -> m.getMenuType() != SysMenuType.TYPE_DIRECTORY)
                .collect(Collectors.toList());
        Map<Long, SysMenuPerm> allMenuPermMap =
                allMenuPermList.stream().collect(Collectors.toMap(SysMenuPerm::getMenuId, m -> m));
        List<Map<String, Object>> menuPermDataList = sysMenuService.getMenuAndPermListByRoleIds(roleIds);
        // 将查询出的菜单权限数据，挂接到完整的菜单树上。
        for (Map<String, Object> menuPermData : menuPermDataList) {
            Long menuId = (Long) menuPermData.get("menuId");
            SysMenuPerm menuPerm = allMenuPermMap.get(menuId);
            menuPerm.getPermUrlSet().add(menuPermData.get("url").toString());
        }
        // 根据菜单的上下级关联关系，将菜单列表还原为菜单树。
        List<TreeNode<SysMenuPerm, Long>> menuTreeList =
                TreeNode.build(allMenuPermList, SysMenuPerm::getMenuId, SysMenuPerm::getParentId, null);
        Set<String> permSet = new HashSet<>();
        String sessionId = TokenData.takeFromRequest().getSessionId();
        // 递归菜单树上每个菜单节点，将子菜单关联的所有permUrlSet，都合并到一级菜单的permUrlSet中。
        for (TreeNode<SysMenuPerm, Long> treeNode : menuTreeList) {
            this.buildAllSubMenuPermUrlSet(treeNode.getChildList(), treeNode.getData().getPermUrlSet());
            permSet.addAll(treeNode.getData().getPermUrlSet());
            this.putMenuPermToCache(sessionId, treeNode.getData());
        }
        return permSet;
    }

    private void putMenuPermToCache(String sessionId, SysMenuPerm menuPerm) {
        if (dataFilterProperties.getEnabledDataPermFilter()) {
            String menuPermKey = RedisKeyUtil.makeSessionMenuPermKey(sessionId, menuPerm.getMenuId());
            RBucket<String> cachedData = redissonClient.getBucket(menuPermKey);
            cachedData.set(JSON.toJSONString(menuPerm.getPermUrlSet()));
            cachedData.expire(appConfig.getSessionExpiredSeconds(), TimeUnit.SECONDS);
        }
    }

    private void buildAllSubMenuPermUrlSet(List<TreeNode<SysMenuPerm, Long>> subList, Set<String> rootPermUrlSet) {
        for (TreeNode<SysMenuPerm, Long> treeNode : subList) {
            rootPermUrlSet.addAll(treeNode.getData().getPermUrlSet());
            if (CollUtil.isNotEmpty(treeNode.getChildList())) {
                this.buildAllSubMenuPermUrlSet(treeNode.getChildList(), rootPermUrlSet);
            }
        }
    }

    private TokenData buildTokenData(SysUser user, String sessionId, int deviceType) {
        TokenData tokenData = new TokenData();
        tokenData.setSessionId(sessionId);
        tokenData.setUserId(user.getUserId());
        tokenData.setOrgId(user.getOrgId());
        // tokenData.setDeptId(user.getDeptId());
        // if (user.getDeptId() != null) {
        // SysDept dept = sysDeptService.getById(user.getDeptId());
        // if (dept != null) {
        // tokenData.setDeptName(dept.getDeptName());
        // }
        // }
        List<SysUserDept> userDeptList = sysUserDeptMapper
                .selectList(Wrappers.lambdaQuery(SysUserDept.class).eq(SysUserDept::getUserId, user.getUserId()));
        if (CollUtil.isNotEmpty(userDeptList)) {
            List<Long> deptIds = userDeptList.stream().map(SysUserDept::getDeptId).collect(Collectors.toList());
            tokenData.setDeptIds(deptIds);
            List<SysDept> deptList = sysDeptMapper.selectBatchIds(deptIds);
            List<SysUserDeptVO> deptVOList = MyModelUtil.copyCollectionTo(deptList, SysUserDeptVO.class);
            tokenData.setDepartments(JSON.parseArray(JSON.toJSONString(deptVOList)));
        }
        tokenData.setLoginName(user.getLoginName());
        tokenData.setShowName(user.getShowName());
        tokenData.setPhone(user.getPhone());
        tokenData.setCardNo(user.getCardNo());
        tokenData.setSex(user.getSex());
        tokenData.setIsAdmin(user.getUserType().equals(SysUserType.TYPE_ADMIN));
        tokenData.setLoginIp(IpUtil.getRemoteIpAddress(ContextUtil.getHttpRequest()));
        tokenData.setLoginTime(new Date());
        tokenData.setDeviceType(deviceType);
        tokenData.setHeadImageUrl(user.getHeadImageUrl());
        tokenData.setFacePicture(user.getFacePicture());
        tokenData.setJobNumber(user.getLoginName());
        tokenData.setShortPhone(user.getShortPhone());
        List<SysUserPost> userPostList = sysPostService.getSysUserPostListByUserId(user.getUserId());
        if (CollUtil.isNotEmpty(userPostList)) {
            Set<Long> deptPostIdSet = userPostList.stream().map(SysUserPost::getDeptPostId).collect(Collectors.toSet());
            tokenData.setDeptPostIds(StrUtil.join(",", deptPostIdSet));
            Set<Long> postIdSet = userPostList.stream().map(SysUserPost::getPostId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(postIdSet)) {
                tokenData.setPostIds(StrUtil.join(",", postIdSet));
                List<SysPost> sysPosts = sysPostService.listByIds(postIdSet);
                tokenData.setPosts(JSON.parseArray(JSON.toJSONString(sysPosts)));
            }
        }
        List<SysUserRole> userRoleList = sysRoleService.getSysUserRoleListByUserId(user.getUserId());
        if (CollUtil.isNotEmpty(userRoleList)) {
            Set<Long> userRoleIdSet = userRoleList.stream().map(SysUserRole::getRoleId).collect(Collectors.toSet());
            tokenData.setRoleIds(StrUtil.join(",", userRoleIdSet));
        }
        return tokenData;
    }

    @Override
    public ResponseResult<String> noPasswordLogin(OaNoPasswordLoginDTO loginParam) {
        SysUser user = sysUserService.getSysUserByOaUserId(loginParam.getOaType(),
                iOaUserApi.getOaUserIdByCode(loginParam.getCode()));
        if (user == null || user.getUserStatus() == null || user.getUserStatus() == SysUserStatus.STATUS_LOCKED) {
            return ResponseResult.error(ErrorCodeEnum.INVALID_USER_STATUS, "登录失败，用户账号被锁定！");
        }
        JSONObject jsonData = this.buildLoginData(user);
        return ResponseResult.success(jsonData.getString(TokenData.REQUEST_ATTRIBUTE_NAME));
    }

    @Override
    public ResponseResult<Void> updatePassword(String oldPass, String newPass) throws UnsupportedEncodingException {
        // 密码强度校验
        boolean isStrongPassword = CheckPwdUtil.isStrongPassword(newPass);
        if (!isStrongPassword) {
            return ResponseResult.error(ErrorCodeEnum.PASSWORD_STRENGTH_CHECK);
        }
        if (MyCommonUtil.existBlankArgument(newPass, oldPass)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        TokenData tokenData = TokenData.takeFromRequest();
        SysUser user = sysUserService.getById(tokenData.getUserId());
        oldPass = URLDecoder.decode(oldPass, StandardCharsets.UTF_8.name());
        // NOTE: 第一次使用时，请务必阅读ApplicationConstant.PRIVATE_KEY的代码注释。
        // 执行RsaUtil工具类中的main函数，可以生成新的公钥和私钥。
        // oldPass = RsaUtil.decrypt(oldPass, ApplicationConstant.PRIVATE_KEY);
        // oldPass = passwordEncoder.encode(oldPass);
        if (user == null || !passwordEncoder.matches(oldPass, user.getPassword())) {
            return ResponseResult.error(ErrorCodeEnum.INVALID_PASSWORD);
        }
        newPass = URLDecoder.decode(newPass, StandardCharsets.UTF_8.name());
        // newPass = RsaUtil.decrypt(newPass, ApplicationConstant.PRIVATE_KEY);
        // newPass = passwordEncoder.encode(newPass);
        if (!sysUserService.changePassword(tokenData.getUserId(), newPass)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    @Override
    public MyPageData<SysUserListVO> listOfDiffDept(SysUserQueryDTO sysUserQueryDTO) {
        if (sysUserQueryDTO.getPageNum() != null && sysUserQueryDTO.getPageSize() != null) {
            PageHelper.startPage(sysUserQueryDTO.getPageNum(), sysUserQueryDTO.getPageSize());
        }
        List<SysUserListVO> list = sysUserMapper.listOfDiffDept(sysUserQueryDTO);
        return MyPageUtil.makeResponseData(list);
    }

    @Override
    public ResponseResult<Void> changeShortPhone(String shortPhone) {
        SysUser updatedUser = new SysUser();
        updatedUser.setUserId(TokenData.takeFromRequest().getUserId());
        updatedUser.setShortPhone(shortPhone);
        sysUserMapper.updateById(updatedUser);
        TokenData.takeFromRequest().setShortPhone(shortPhone);
        putTokenDataToSessionCache(TokenData.takeFromRequest());
        return ResponseResult.success();
    }

    @Override
    @Cacheable(value = RedisKeyConstant.SYSTEM_BASEINFO_USER, key = "#deviceSn")
    public SysUser selectUserBydeviceSn(String deviceSn) {
        return sysUserService.getOne(Wrappers.lambdaQuery(SysUser.class).eq(SysUser::getDeviceSn, deviceSn));
    }

    @Override
    public List<SysUser> getUserByTagCode(String tagCode) {
        return sysUserMapper.getUserByTagCode(tagCode);
    }

}
