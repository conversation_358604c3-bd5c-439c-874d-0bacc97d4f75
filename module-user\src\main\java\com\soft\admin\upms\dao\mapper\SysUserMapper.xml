<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.admin.upms.dao.SysUserMapper">
    <resultMap id="BaseResultMap" type="com.soft.admin.upms.model.SysUser">
        <id column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="show_name" jdbcType="VARCHAR" property="showName"/>
        <result column="sex" jdbcType="INTEGER" property="sex"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="short_phone" jdbcType="VARCHAR" property="shortPhone"/>
        <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="user_type" jdbcType="INTEGER" property="userType"/>
        <result column="head_image_url" jdbcType="VARCHAR" property="headImageUrl"/>
        <result column="user_status" jdbcType="INTEGER" property="userStatus"/>
        <result column="face_picture" jdbcType="VARCHAR" property="facePicture"/>
        <result column="one_card_no" jdbcType="VARCHAR" property="oneCardNo"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime"/>
        <result column="deleted_flag" jdbcType="INTEGER" property="deletedFlag"/>
    </resultMap>

    <sql id="columns">
        t1
        .
        user_id
        ,
        t1.login_name,
        t1.password,
        t1.show_name,
        t1.phone,
        t1.short_phone,
        t1.sex,
        t1.card_no,
        t1.dept_id,
        t1.user_type,
        t1.head_image_url,
        t1.user_status,
        t1.face_picture,
        t1.one_card_no,
        t1.deleted_flag,
        t1.create_user_id,
        t1.update_user_id,
        t1.create_time,
        t1.update_time,
        t1.last_login_time
    </sql>

    <insert id="insertList">
        INSERT INTO common_sys_user
        (user_id,
        login_name,
        password,
        show_name,
        phone,
        card_no,
        dept_id,
        user_type,
        user_tags,
        head_image_url,
        user_status,
        face_picture,
        one_card_no,
        deleted_flag,
        create_user_id,
        update_user_id,
        create_time,
        update_time)
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.userId},
            #{item.loginName},
            #{item.password},
            #{item.showName},
            #{item.phone},
            #{item.cardNo},
            #{item.deptId},
            #{item.userType},
            #{item.userTags},
            #{item.headImageUrl},
            #{item.userStatus},
            #{item.facePicture},
            #{item.oneCardNo},
            #{item.deletedFlag},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.createTime},
            #{item.updateTime})
        </foreach>
    </insert>
    <update id="updateUser">
        update
        common_sys_user
        set
        login_name = #{user.loginName},
        <if test="user.password != null and user.password != ''">
            password = #{user.password},
        </if>
        show_name = #{user.showName},
        phone = #{user.phone},
        card_no = #{user.cardNo},
        dept_id = #{user.deptId},
        user_type = #{user.userType},
        <if test="user.headImageUrl != null and user.headImageUrl != ''">
            head_image_url = #{user.headImageUrl},
        </if>
        <if test="user.userTags != null and user.userTags != ''">
            user_tags = #{user.userTags},
        </if>
        user_status = #{user.userStatus},
        <if test="user.facePicture != null and user.facePicture != ''">
            face_picture = #{user.facePicture},
        </if>
        <if test="user.oneCardNo != null and user.oneCardNo != ''">
            one_card_no = #{user.oneCardNo},
        </if>
        <if test="user.lastLoginTime != null">
            last_login_time = #{user.lastLoginTime},
        </if>
        deleted_flag = #{user.deletedFlag},
        update_user_id = #{user.updateUserId},
        update_time = now()
        where
        user_id = #{user.userId}
    </update>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="com.soft.admin.upms.dao.SysUserMapper.inputFilterRef"/>
        AND common_sys_user.deleted_flag = ${@com.soft.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件  -->
    <!--  AND (EXISTS (SELECT 1 FROM common_sys_dept_relation WHERE
                        common_sys_dept_relation.parent_dept_id = #{sysUserFilter.deptId}
                        AND common_sys_user.dept_id = common_sys_dept_relation.dept_id))-->
    <sql id="inputFilterRef">
        <if test="sysUserFilter != null">
            <if test="sysUserFilter.loginName != null and sysUserFilter.loginName != ''">
                <bind name="safeSysUserLoginName" value="'%' + sysUserFilter.loginName + '%'"/>
                AND common_sys_user.login_name LIKE #{safeSysUserLoginName}
            </if>
            <if test="sysUserFilter.showName != null and sysUserFilter.showName != ''">
                <bind name="safeSysUserShowName" value="'%' + sysUserFilter.showName + '%'"/>
                AND common_sys_user.show_name LIKE #{safeSysUserShowName}
            </if>
            <if test="sysUserFilter.deptId != null">
                and exists (select 1 from common_sys_dept where (dept_id=#{sysUserFilter.deptId} or
                parent_id=#{sysUserFilter.deptId}) and dept_id=common_sys_user.dept_id)
            </if>
            <if test="sysUserFilter.userStatus != null">
                AND common_sys_user.user_status = #{sysUserFilter.userStatus}
            </if>
            <if test="sysUserFilter.createTimeStart != null and sysUserFilter.createTimeStart != ''">
                AND common_sys_user.create_time &gt;= #{sysUserFilter.createTimeStart}
            </if>
            <if test="sysUserFilter.createTimeEnd != null and sysUserFilter.createTimeEnd != ''">
                AND common_sys_user.create_time &lt;= #{sysUserFilter.createTimeEnd}
            </if>
        </if>
    </sql>

    <select id="getSysUserList" resultMap="BaseResultMap" parameterType="com.soft.admin.upms.model.SysUser">
        SELECT * FROM common_sys_user
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserApiList" resultType="com.soft.admin.upms.vo.ApiSysUserVo"
            parameterType="com.soft.admin.upms.model.SysUser">
        SELECT user_id,show_name,dept_id,face_picture,one_card_no,user_status,create_time,login_name,phone,deleted_flag
        FROM common_sys_user
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByRoleId" resultMap="BaseResultMap">
        SELECT
        common_sys_user.*
        FROM
        common_sys_user_role,
        common_sys_user
        <where>
            AND common_sys_user_role.role_id = #{roleId}
            AND common_sys_user_role.user_id = common_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInSysUserListByRoleId" resultMap="BaseResultMap">
        SELECT * FROM common_sys_user
        <where>
            NOT EXISTS (SELECT * FROM common_sys_user_role
            WHERE common_sys_user_role.role_id = #{roleId} AND common_sys_user_role.user_id = common_sys_user.user_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByDataPermId" resultMap="BaseResultMap">
        SELECT
        common_sys_user.*
        FROM
        common_sys_data_perm_user,
        common_sys_user
        <where>
            AND common_sys_data_perm_user.data_perm_id = #{dataPermId}
            AND common_sys_data_perm_user.user_id = common_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInSysUserListByDataPermId" resultMap="BaseResultMap">
        SELECT * FROM common_sys_user
        <where>
            NOT EXISTS (SELECT * FROM common_sys_data_perm_user
            WHERE common_sys_data_perm_user.data_perm_id = #{dataPermId} AND common_sys_data_perm_user.user_id =
            common_sys_user.user_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByDeptPostId" resultMap="BaseResultMap">
        SELECT
        common_sys_user.*
        FROM
        common_sys_user_post,
        common_sys_user
        <where>
            AND common_sys_user_post.dept_post_id = #{deptPostId}
            AND common_sys_user_post.user_id = common_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getNotInSysUserListByDeptPostId" resultMap="BaseResultMap">
        SELECT * FROM common_sys_user
        <where>
            NOT EXISTS (SELECT * FROM common_sys_user_post
            WHERE common_sys_user_post.dept_post_id = #{deptPostId} AND common_sys_user_post.user_id =
            common_sys_user.user_id)
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSysUserListByPostId" resultMap="BaseResultMap">
        SELECT
        common_sys_user.*
        FROM
        common_sys_user_post,
        common_sys_user
        <where>
            AND common_sys_user_post.post_id = #{postId}
            AND common_sys_user_post.user_id = common_sys_user.user_id
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <!-- 以下查询仅用于权限分配的问题定位，由于关联表较多，可能会给系统运行带来性能影响 -->
    <select id="getSysPermListWithDetail" resultType="map">
        SELECT
        r.role_id roleId,
        r.role_name roleName,
        m.menu_id menuId,
        m.menu_name menuName,
        m.menu_type menuType,
        pc.perm_code_id permCodeId,
        pc.perm_code permCode,
        pc.perm_code_type permCodeType,
        p.url url
        FROM
        common_sys_user_role ur,
        common_sys_role r,
        common_sys_role_menu rm,
        common_sys_menu m,
        common_sys_menu_perm_code mpc,
        common_sys_perm_code pc,
        common_sys_perm_code_perm pcp,
        common_sys_perm p
        <where>
            AND ur.user_id = #{userId}
            AND ur.role_id = r.role_id
            AND ur.role_id = rm.role_id
            AND rm.menu_id = m.menu_id
            AND rm.menu_id = mpc.menu_id
            AND mpc.perm_code_id = pc.perm_code_id
            AND mpc.perm_code_id = pcp.perm_code_id
            AND pcp.perm_id = p.perm_id
            <if test="url != null and url != ''">
                AND p.url = #{url}
            </if>
        </where>
        ORDER BY
        r.role_id, m.menu_id, pc.perm_code_id, p.url
    </select>

    <select id="getSysPermCodeListWithDetail" resultType="map">
        SELECT
        r.role_id roleId,
        r.role_name roleName,
        m.menu_id menuId,
        m.menu_name menuName,
        m.menu_type menuType,
        pc.perm_code_id permCodeId,
        pc.perm_code permCode,
        pc.perm_code_type permCodeType
        FROM
        common_sys_user_role ur,
        common_sys_role r,
        common_sys_role_menu rm,
        common_sys_menu m,
        common_sys_menu_perm_code mpc,
        common_sys_perm_code pc
        <where>
            AND ur.user_id = #{userId}
            AND ur.role_id = r.role_id
            AND ur.role_id = rm.role_id
            AND rm.menu_id = m.menu_id
            AND rm.menu_id = mpc.menu_id
            AND mpc.perm_code_id = pc.perm_code_id
            <if test="permCode != null and permCode != ''">
                AND pc.perm_code = #{permCode}
            </if>
        </where>
        ORDER BY
        r.role_id, m.menu_id, pc.perm_code_id
    </select>

    <select id="getSysMenuListWithDetail" resultType="map">
        SELECT
        r.role_id roleId,
        r.role_name roleName,
        m.menu_id menuId,
        m.menu_name menuName,
        m.menu_type menuType
        FROM
        common_sys_user_role ur,
        common_sys_role r,
        common_sys_role_menu rm,
        common_sys_menu m
        <where>
            AND ur.user_id = #{userId}
            AND ur.role_id = r.role_id
            AND ur.role_id = rm.role_id
            AND rm.menu_id = m.menu_id
            <if test="menuName != null and menuName != ''">
                AND m.menu_name = #{menuName}
            </if>
        </where>
        ORDER BY
        r.role_id, m.menu_id
    </select>
    <select id="selectSysUserByOaUserId" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from
        common_sys_user as t1
        left join
        sp_oa_user as t2 using (user_id)
        where
        t1.deleted_flag = 1
        and
        t2.oa_type = #{oaType}
        and
        t2.oa_user_id = #{oaUserId}
    </select>
    <select id="selectHasPhoneUserByProjectId" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from
        common_sys_user t1
        left join
        common_sys_user_project as t2 using (user_id)
        where
        t1.phone &lt;&gt; ''
        and
        t1.phone is not null
        order by
        t1.create_time asc
    </select>
    <select id="selectHasPhoneUser" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from
        common_sys_user t1
        where
        t1.phone &lt;&gt; ''
        and
        t1.phone is not null
        and
        t1.user_status=0
        and
        t1.deleted_flag=1
        order by
        t1.create_time asc
    </select>
    <select id="getUserByDelList" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from
        common_sys_user t1
        where
        t1.phone &lt;&gt; ''
        and
        t1.phone is not null
        and
        (t1.user_status=1 or deleted_flag=-1)
        order by
        t1.create_time asc
    </select>
    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from
        common_sys_user t1
        where
        t1.user_id = #{userId}
    </select>

    <select id="selectByLoginNames" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from
        common_sys_user t1
        where
        login_name in
        <foreach collection="list" item="loginName" open="(" separator="," close=")">
            #{loginName}
        </foreach>
    </select>

    <select id="queryList" resultType="com.soft.admin.upms.vo.SysUserListVO">
        select csu.user_id,csu.device_sn, csu.login_name, csu.show_name, csu.sex, csu.user_type,
        csu.short_phone,csu.phone,
        CASE WHEN r.leader = 0 THEN '普通用户' WHEN r.leader = 1 THEN '主管' ELSE '--' END AS isManager,
        csu.user_status, csu.create_time, csu.last_login_time, csu.dept_id, csd.dept_name,
        GROUP_CONCAT(DISTINCT csr.role_name ORDER BY csr.role_id SEPARATOR ',') AS roleNames,
        GROUP_CONCAT(DISTINCT CONCAT(csd_post.dept_name, '|', csp.post_name) ORDER BY csp.post_id SEPARATOR ',') AS
        postNames,
        GROUP_CONCAT(DISTINCT csd.dept_name ORDER BY csd.dept_id SEPARATOR ',') AS deptNames
        FROM
        common_sys_user csu
        LEFT JOIN common_sys_dept_user r ON r.user_id = csu.user_id
        LEFT JOIN common_sys_dept csd ON r.dept_id = csd.dept_id
        AND csd.deleted_flag = 1
        LEFT JOIN common_sys_user_role csur ON csu.user_id = csur.user_id
        LEFT JOIN common_sys_role csr ON csur.role_id = csr.role_id
        AND csr.delete_flag = 1
        LEFT JOIN common_sys_user_post csup ON csu.user_id = csup.user_id
        LEFT JOIN common_sys_dept_post csdp ON csup.dept_post_id = csdp.dept_post_id
        LEFT JOIN common_sys_post csp ON csdp.post_id = csp.post_id
        AND csp.deleted_flag = 1
        LEFT JOIN common_sys_dept csd_post ON csdp.dept_id = csd_post.dept_id
        AND csd_post.deleted_flag = 1
        <where>
            csu.deleted_flag = 1
            <if test="query.showName != null and query.showName != ''">
                and (csu.show_name like concat('%', #{query.showName}, '%') or csu.login_name like concat('%',
                #{query.showName}, '%') )
            </if>
            <if test="query.roleId != null">
                and csr.role_id = #{query.roleId}
            </if>
            <if test="query.userStatus != null">
                and csu.user_status = #{query.userStatus}
            </if>
            <if test="query.deptId != null">
                and csd.dept_id = #{query.deptId}
            </if>
            <if test="query.postId != null">
                and csp.post_id = #{query.postId}
            </if>
        </where>
        group by csu.user_id
        order by csu.create_time desc
    </select>


    <select id="listByDeptId" resultType="com.soft.admin.upms.vo.SysUserListVO">
        select csu.user_id, csu.login_name, csu.show_name, csu.sex, csu.phone, csu.user_type, csu.short_phone,
        csu.user_status, csu.create_time, csu.last_login_time, csd.dept_name,
        group_concat(distinct csr.role_name order by csr.role_id) as roleNames,
        group_concat(distinct csp.post_name order by csp.post_id) as postNames
        from common_sys_user csu
        left join common_sys_dept_user r on r.user_id = csu.user_id
        left join common_sys_dept csd on r.dept_id = csd.dept_id and csd.deleted_flag = 1
        left join common_sys_user_role csur on csu.user_id = csur.user_id
        left join common_sys_role csr on csur.role_id = csr.role_id and csr.delete_flag = 1
        left join common_sys_user_post csup on csu.user_id = csup.user_id
        left join common_sys_post csp on csup.post_id = csp.post_id and csp.deleted_flag = 1
        <where>
            csu.deleted_flag = 1
            <if test="query.showName != null and query.showName != ''">
                and csu.show_name like concat('%', #{query.showName}, '%')
            </if>
            <if test="query.roleId != null">
                and csr.role_id = #{query.roleId}
            </if>
            <if test="query.deptId != null">
                and csd.dept_id = #{query.deptId}
            </if>
            <if test="query.postId != null">
                and csp.post_id = #{query.postId}
            </if>
        </where>
        group by csu.user_id
        order by csu.create_time desc
    </select>

    <select id="listOfDiffDept" resultType="com.soft.admin.upms.vo.SysUserListVO">
        select csu.user_id, csu.login_name, csu.show_name, csu.sex, csu.phone, csu.short_phone, csu.user_type,
        csu.user_status, csu.create_time, csu.last_login_time, csd.dept_id, csd.dept_name
        from common_sys_user csu
        left join common_sys_dept_user r on r.user_id = csu.user_id
        left join common_sys_dept csd on r.dept_id = csd.dept_id and csd.deleted_flag = 1
        <where>
            csu.deleted_flag = 1
            <if test="query.showName != null and query.showName != ''">
                and csu.show_name like concat('%', #{query.showName}, '%')
            </if>
            <if test="query.deptId != null">
                and r.dept_id = #{query.deptId}
            </if>
        </where>
        order by csu.user_id desc
    </select>

    <select id="getUserByTagCode" resultType="com.soft.admin.upms.model.SysUser">
        select a.user_id,
               a.show_name,
               a.device_sn,
               (select GROUP_CONCAT(y.dept_name)
                from common_sys_dept_user x,
                     common_sys_dept y
                where x.dept_id = y.dept_id
                  and x.user_id = a.user_id) as dept_name
        from common_sys_user a,
             common_sys_user_tag b,
             common_sys_tag c
        where a.user_id = b.user_id
          and b.tag_id = c.id
          and c.`code` = #{tagCode};
    </select>

</mapper>
