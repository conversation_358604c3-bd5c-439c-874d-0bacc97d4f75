package com.rutong.medical.admin.dto.defence;

import com.rutong.medical.admin.entity.device.Device;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025-07-29
 */
@Data
public class DefenceSaveOrUpdateDTO {

    /**
     * 防区id
     */
    private Long invadeDefenceId;

    /**
     * 防区编号
     */
    private String defenceCode;

    /**
     * 防区名称
     */
    private String defenceName;


    /**
     * 防区状态
     */
    private Integer defenceState;

    /**
     * 布防开始时间
     */
    private LocalTime startTime;

    /**
     * 布防结束时间
     */
    private LocalTime endTime;

    private Long createUserId;
    private LocalDate createTime;
    private Long updateUserId;
    private LocalDate updateTime;
    private Integer isDelete;

    /**
     * 添加设备
     */
    List<Long> deviceIdss;

}
