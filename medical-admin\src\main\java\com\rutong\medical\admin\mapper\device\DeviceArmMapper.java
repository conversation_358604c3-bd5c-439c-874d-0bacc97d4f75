package com.rutong.medical.admin.mapper.device;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rutong.medical.admin.dto.device.DeviceArmDTO;
import com.rutong.medical.admin.entity.device.DeviceArm;

/**
 * <AUTHOR>
 * @Date 2025-08-01
 */

public interface DeviceArmMapper extends BaseMapper<DeviceArm> {
    boolean updateState(DeviceArmDTO deviceArmDTO);

    boolean autoArm(DeviceArmDTO deviceArmDTO);
}
