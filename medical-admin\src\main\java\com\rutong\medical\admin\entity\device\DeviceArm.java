package com.rutong.medical.admin.entity.device;

import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;

import java.time.LocalTime;

/**
 * 设备布防表
 *
 * <AUTHOR>
 * @Date 2025-08-01
 */
@Data
@TableName(value = "sm_device_layout_defence")
public class DeviceArm extends BaseModel {

    private Long id;

    private Long deviceId;

    private Long invadeDefenceId;

    private Integer defenceState;

    private LocalTime startTime;

    private LocalTime endTime;

}
