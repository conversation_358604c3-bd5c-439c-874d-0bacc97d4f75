package com.rutong.medical.admin.entity.defence;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @Date 2025-07-19
 */

@Data
@TableName("sm_device_layout_defence")
public class SmDeviceLayoutDefence {

    /**
     * 防区设备表id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 防区id
     */
    private Long invadeDefenceId;

    /**
     * 设备id
     */
    private Long deviceId;

    private Long defenceState;
    private LocalTime startTime;
    private LocalTime endTime;

    private Long createUserId;
    private LocalDate createTime;

    private Long updateUserId;
    private LocalDate updateTime;

}
