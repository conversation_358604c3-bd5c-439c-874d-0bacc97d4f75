package com.rutong.medical.admin.service.location;

import com.rutong.medical.admin.dto.station.BaseStationDataDTO;
import com.rutong.medical.admin.entity.location.UserLocation;
import com.rutong.medical.admin.vo.location.UserLocationVO;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * @ClassName LocationService
 * @Description 人员定位
 * <AUTHOR>
 * @Date 2025/7/15 10:27
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
public interface UserLocationService {

    /**
     *工卡定位信息
     * @return
     */
    Boolean cardSave(BaseStationDataDTO baseStationDataDTO) throws InvocationTargetException, IllegalAccessException;

    /**
     *报警器
     * @return
     */
    Boolean buttonSave(BaseStationDataDTO baseStationDataDTO) throws InvocationTargetException, IllegalAccessException;

    /**
     *红外探测器
     * @return
     */
    Boolean infraredMonitorSave(BaseStationDataDTO baseStationDataDTO) throws InvocationTargetException, IllegalAccessException;

    /**
     * 单个用户订阅
     * @param userId
     * @return
     */
    Boolean subscriptionSingle(Long userId);

    /**
     * 获取所有安保人员定位信息
     * @return
     */
    List<UserLocationVO> getSecurityAll();

    /**
     * 根据楼层获取医疗人员
     * @return
     */
    List<UserLocationVO> getUserLocationByFloor(Long floorId,String tagCode);

}
