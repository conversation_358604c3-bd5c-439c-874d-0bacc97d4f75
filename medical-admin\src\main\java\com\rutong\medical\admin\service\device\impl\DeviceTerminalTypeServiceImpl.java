package com.rutong.medical.admin.service.device.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rutong.medical.admin.mapper.device.DeviceTypeBusinessMapper;
import com.rutong.medical.admin.vo.device.DeviceTerminalTypeVO;
import com.rutong.medical.admin.vo.device.DeviceTypeBusinessVO;
import com.soft.common.core.util.MyModelUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rutong.medical.admin.dto.device.DeviceTerminalTypeDTO;
import com.rutong.medical.admin.dto.device.DeviceTerminalTypeQueryDTO;
import com.rutong.medical.admin.entity.device.Device;
import com.rutong.medical.admin.entity.device.DeviceTerminalType;
import com.rutong.medical.admin.entity.device.DeviceTypeBusiness;
import com.rutong.medical.admin.mapper.device.DeviceMapper;
import com.rutong.medical.admin.mapper.device.DeviceTerminalTypeMapper;
import com.rutong.medical.admin.service.device.DeviceTerminalTypeService;
import com.rutong.medical.admin.service.device.DeviceTypeBusinessService;
import com.soft.common.core.constant.GlobalDeletedFlag;
import com.soft.common.core.exception.ServiceException;
import com.soft.common.core.object.TokenData;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;

/**
 * 设备分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Service
public class DeviceTerminalTypeServiceImpl extends ServiceImpl<DeviceTerminalTypeMapper, DeviceTerminalType>
        implements DeviceTerminalTypeService {

    @Autowired
    private DeviceTerminalTypeMapper smDeviceTerminalTypeMapper;
    @Resource
    private DeviceMapper deviceMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private DeviceTypeBusinessService deviceTypeBusinessService;

    @Resource
    private DeviceTypeBusinessMapper deviceTypeBusinessMapper;

    public static final String smartMedicalTypeCode = "smart:medical:device:typeCode:";

    @Override
    public List<Tree<Long>> getTreeList(DeviceTerminalTypeQueryDTO deviceTerminalTypeQuery) {
        // 根据条件查询设备类型列表
        List<DeviceTerminalType> typeList = smDeviceTerminalTypeMapper.selectList(
                new LambdaQueryWrapper<DeviceTerminalType>().eq(DeviceTerminalType::getIsDelete, GlobalDeletedFlag.NORMAL)
                        .eq(StringUtils.isNotEmpty(deviceTerminalTypeQuery.getTypeCode()), DeviceTerminalType::getTypeCode,
                                deviceTerminalTypeQuery.getTypeCode())
                        .like(StringUtils.isNotBlank(deviceTerminalTypeQuery.getTypeName()), DeviceTerminalType::getTypeName,
                                deviceTerminalTypeQuery.getTypeName())
                        .orderByDesc(DeviceTerminalType::getCreateTime));

        // 查询所有type_code对应的business_code，生成map key是type_code value是business_code多个逗号拼接
        List<DeviceTypeBusiness> businessList = deviceTypeBusinessService.list();
        Map<String, String> businessCodeMap =
                businessList.stream().collect(Collectors.groupingBy(DeviceTypeBusiness::getTypeCode,
                        Collectors.mapping(DeviceTypeBusiness::getBusinessCode, Collectors.joining(","))));

        // 组装成树结构
        TreeNodeConfig config = new TreeNodeConfig();
        return TreeUtil.build(typeList, 0L, config, ((type, tree) -> {
            tree.setId(type.getId());
            tree.setName(type.getTypeName());
            tree.setParentId(type.getParentId());
            tree.setWeight(0);
            tree.putExtra("typeCode", type.getTypeCode());
            tree.putExtra("iconPath", type.getIconPath());
            tree.putExtra("businessCode", businessCodeMap.getOrDefault(type.getTypeCode(), ""));
        }));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(DeviceTerminalTypeDTO deviceTerminalTypeDTO) {
        String typeName = deviceTerminalTypeDTO.getTypeName();
        // 校验：type_name 是否已存在
        boolean exists = smDeviceTerminalTypeMapper
                .selectCount(new LambdaQueryWrapper<DeviceTerminalType>().eq(DeviceTerminalType::getTypeName, typeName)
                        .eq(DeviceTerminalType::getIsDelete, GlobalDeletedFlag.NORMAL)) > 0;

        if (exists) {
            throw new ServiceException("分类名称 [" + typeName + "] 已存在，请勿重复添加");
        }

        DeviceTerminalType entity = new DeviceTerminalType();

        // 设置 type_code
        entity.setTypeCode(deviceTerminalTypeDTO.getTypeCode());
        entity.setTypeName(deviceTerminalTypeDTO.getTypeName());

        // 先保存 entity，获取生成的 id
        try {
            entity.setCreateUserId(TokenData.takeFromRequest().getUserId());
            entity.setCreateTime(new Date());
            entity.setIconPath(deviceTerminalTypeDTO.getIconPath());
            entity.setIsDelete(GlobalDeletedFlag.NORMAL);
            smDeviceTerminalTypeMapper.insert(entity);
        } catch (Exception e) {
            throw new ServiceException("添加设备类型失败");
        }

        // 处理父级路径
        if (Objects.nonNull(deviceTerminalTypeDTO.getParentId()) && deviceTerminalTypeDTO.getParentId() != 0L) {
            DeviceTerminalType parentType = smDeviceTerminalTypeMapper.selectById(deviceTerminalTypeDTO.getParentId());
            Assert.notNull(parentType, "上级设备类型不存在");

            // 拼接 path_id
            String parentPathId = parentType.getPathId();
            if (parentPathId == null) {
                parentPathId = "";
            }
            entity.setPathId(parentPathId + "/" + entity.getId());
            entity.setUpdateTime(new Date());
            entity.setParentId(parentType.getId());
            entity.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            // 更新 path_id 字段
            smDeviceTerminalTypeMapper.updateById(entity);
        } else {
            // 如果是根节点，path_id 就是自己的 id
            entity.setPathId(entity.getId().toString());
            // 更新 path_id 字段
            entity.setUpdateTime(new Date());
            entity.setUpdateUserId(TokenData.takeFromRequest().getUserId());
            smDeviceTerminalTypeMapper.updateById(entity);
        }

        saveBatchDeviceTypeBusiness(deviceTerminalTypeDTO, entity);
    }

    /**
     * 新增设备类型业务系统关联
     *
     * @param deviceTerminalTypeDTO
     * @param entity
     */
    public void saveBatchDeviceTypeBusiness(DeviceTerminalTypeDTO deviceTerminalTypeDTO, DeviceTerminalType entity) {
        // 处理业务系统编号（支持多个，逗号分隔）
        if (StringUtils.isNotBlank(deviceTerminalTypeDTO.getBusinessCode())) {
            String[] businessCodes = deviceTerminalTypeDTO.getBusinessCode().split(",");
            List<DeviceTypeBusiness> businessList = new ArrayList<>();
            for (String code : businessCodes) {
                if (StringUtils.isNotBlank(code)) {
                    DeviceTypeBusiness business = new DeviceTypeBusiness();
                    business.setTypeCode(entity.getTypeCode());
                    business.setBusinessCode(code.trim());
                    businessList.add(business);
                }
            }
            if (!businessList.isEmpty()) {
                deviceTypeBusinessService.saveBatch(businessList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DeviceTerminalTypeDTO deviceTerminalType) {
        // 检查目标分类是否存在
        DeviceTerminalType deviceType = smDeviceTerminalTypeMapper.selectById(deviceTerminalType.getId());
        Assert.notNull(deviceType, "分类不存在");

        // 检查上级分类是否存在（如果提供了上级分类ID）
        if (deviceTerminalType.getParentId() != null && deviceTerminalType.getParentId() != 0L) {
            DeviceTerminalType parentType = smDeviceTerminalTypeMapper.selectById(deviceTerminalType.getParentId());
            Assert.notNull(parentType, "上级分类不存在");

            // 防止循环引用（分类不能成为自己的上级）
            if (deviceTerminalType.getId().equals(deviceTerminalType.getParentId())) {
                throw new ServiceException("分类不能设置自己为上级");
            }
        }

        // 更新分类信息
        deviceType.setTypeName(deviceTerminalType.getTypeName());
        deviceType.setTypeCode(deviceTerminalType.getTypeCode());
        deviceType.setParentId(deviceTerminalType.getParentId());
        deviceType.setIconPath(deviceTerminalType.getIconPath());

        // 删除分类业务系统关联
        deviceTypeBusinessService.remove(
                new LambdaQueryWrapper<DeviceTypeBusiness>().eq(DeviceTypeBusiness::getTypeCode, deviceType.getTypeCode()));

        // 新增分类业务系统关联
        saveBatchDeviceTypeBusiness(deviceTerminalType, deviceType);

        try {
            smDeviceTerminalTypeMapper.updateById(deviceType);
        } catch (Exception e) {
            throw new ServiceException("修改设备类型失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        DeviceTerminalType deviceTerminalType = smDeviceTerminalTypeMapper.selectById(id);
        if (deviceTerminalType == null) {
            throw new ServiceException("该分类不存在");
        }

        Long countDevice = deviceMapper.selectCount(new LambdaQueryWrapper<Device>()
                .eq(Device::getDeviceTerminalTypeId, id).eq(Device::getIsDelete, GlobalDeletedFlag.NORMAL));
        if (countDevice > 0) {
            throw new ServiceException("该分类有设备,无法删除");
        }

        DeviceTerminalType parentType = smDeviceTerminalTypeMapper.selectById(deviceTerminalType.getParentId());
        if (parentType == null) {
            boolean exists = new LambdaQueryChainWrapper<>(smDeviceTerminalTypeMapper)
                    .eq(DeviceTerminalType::getIsDelete, GlobalDeletedFlag.NORMAL).eq(DeviceTerminalType::getParentId, id)
                    .last("limit 1").exists();
            if (exists)
                throw new ServiceException("存在下级分类无法删除");
        }
        try {
            deviceTerminalType.setIsDelete(GlobalDeletedFlag.DELETED);
            smDeviceTerminalTypeMapper.updateById(deviceTerminalType);
        } catch (Exception e) {
            throw new ServiceException("删除设备类型失败");
        }
    }

    @Override
    public List<DeviceTerminalTypeVO> getList(String businessCode) {
        return deviceTypeBusinessMapper.selectList(
                        Wrappers.lambdaQuery(DeviceTypeBusiness.class).eq(DeviceTypeBusiness::getBusinessCode, businessCode))
                .stream()
                .map(deviceTypeBusiness -> {
                    DeviceTerminalType deviceTerminalType = smDeviceTerminalTypeMapper.selectOne(Wrappers.lambdaQuery(DeviceTerminalType.class)
                            .eq(DeviceTerminalType::getTypeCode, deviceTypeBusiness.getTypeCode()));
                    if (ObjectUtils.isEmpty(deviceTerminalType)) {
                        return null;
                    }
                    Long count = deviceMapper.selectCount(Wrappers.lambdaQuery(Device.class)
                            .eq(Device::getDeviceTerminalTypeId, deviceTerminalType.getId())
                            .eq(Device::getIsDelete, GlobalDeletedFlag.NORMAL));
                    DeviceTerminalTypeVO deviceTerminalTypeVO = MyModelUtil.copyTo(deviceTerminalType, DeviceTerminalTypeVO.class);
                    deviceTerminalTypeVO.setBusinessCode(deviceTypeBusiness.getBusinessCode());
                    deviceTerminalTypeVO.setDeviceCount(count);
                    return deviceTerminalTypeVO;
                })
                .collect(Collectors.toList());
    }

}
