package com.rutong.medical.admin.vo.alarm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName AlarmDetailMonitorImgVO
 * @Description
 * <AUTHOR>
 * @Date 2025/7/29 11:45
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@ApiModel("AlarmDetailMonitorImgVO视图对象")
@Data
public class AlarmDetailMonitorImgVO {

    @ApiModelProperty(value = "报警监控照片表ID")
    private Long id;

    @ApiModelProperty(value = "报警记录表ID")
    private Long alarmDetailId;

    @ApiModelProperty(value = "视频监控表ID")
    private Long deviceMonitorId;

    @ApiModelProperty(value = "照片路径")
    private String imgPath;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
