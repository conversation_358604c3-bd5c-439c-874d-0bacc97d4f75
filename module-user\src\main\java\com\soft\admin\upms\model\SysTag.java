package com.soft.admin.upms.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.admin.upms.vo.SysTagVO;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.soft.common.core.base.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;

/**
 * 标签对象 sp_tag
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "common_sys_tag")
public class SysTag extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 备注
     */
    private String remark;

    /**
     * 标签类型 1用户标签 2设备标签
     */
    private Integer type;

    /**
     * 删除（0否，1是）
     */
    private Integer deletedFlag;


    @Mapper
    public interface TagModelMapper extends BaseModelMapper<SysTagVO, SysTag> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        SysTag toModel(SysTagVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        SysTagVO fromModel(SysTag entity);
    }

    public static final TagModelMapper INSTANCE = Mappers.getMapper(TagModelMapper.class);
}
