package com.rutong.medical.admin.entity.alarm;

import java.util.Date;

import com.rutong.medical.admin.vo.alarm.AlarmDetailVO;
import lombok.Data;
import com.soft.common.core.base.model.BaseModel;
import com.soft.common.core.base.mapper.BaseModelMapper;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.EqualsAndHashCode;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName AlarmDetail
 * @Description
 * <AUTHOR>
 * @Date 2025/7/16 14:38
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@Data
@TableName(value = "sm_alarm_detail")
public class AlarmDetail {

    /** 报警记录表ID */
    @TableId(value = "id")
    private Long id;

    /** 基站表ID */
    private String deviceBaseStationSn;

    /** 用户表ID */
    private Long userId;

    /** 设备sn */
    private String deviceSn;

    /** 业务系统编号 */
    private String businessCode;

    /** 用户名称 */
    private String userName;

    /** 设备类型(7:报警器,8:红外探测,9:工卡) */
    private String deviceTypeCode;

    /** 报警类型(1:呼叫报警,2:低电量) */
    private Byte alarmType;

    /** 处理状态(0:未确认,1:已确认) */
    private Byte disposeState;

    /** 上报位置 */
    private Long spaceId;

    /** 报警时间 */
    private Date alarmTime;

    /** 处理时间 */
    private Date disposeTime;


    @Mapper
    public interface AlarmDetailModelMapper extends BaseModelMapper<AlarmDetailVO, AlarmDetail> {
        /**
         * 转换Vo对象到实体对象。
         *
         * @param entityVo 域对象。
         * @return 实体对象。
         */
        @Override
        AlarmDetail toModel(AlarmDetailVO entityVo);

        /**
         * 转换实体对象到VO对象。
         *
         * @return entity 域对象。
         */
        @Override
        AlarmDetailVO fromModel(AlarmDetail entity);
    }

    public static final AlarmDetailModelMapper INSTANCE = Mappers.getMapper(AlarmDetailModelMapper.class);
}
