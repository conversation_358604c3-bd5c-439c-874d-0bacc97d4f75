package com.rutong.medical.admin.controller.location;

import com.rutong.medical.admin.dto.location.UserLocationDTO;
import com.rutong.medical.admin.entity.system.Space;
import com.rutong.medical.admin.service.location.UserTrackService;
import com.rutong.medical.admin.vo.location.UserLocationVO;
import com.soft.common.core.object.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @ClassName UserTrackController
 * @Description 移动轨迹
 * <AUTHOR>
 * @Date 2025/7/25 10:40
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@RestController
@RequestMapping("/track/")
@Api(tags = "移动轨迹")
@AllArgsConstructor
public class UserTrackController {

    private UserTrackService userTrackService;

    /**
     * 获取用户移动轨迹
     * @param userLocationDTO
     * @return
     */
    @GetMapping("getUserTrack")
    @ApiOperation(value = "获取用户移动轨迹")
    public ResponseResult<Map<Space, List<UserLocationVO>>> getUserTrack(UserLocationDTO userLocationDTO){
        return ResponseResult.success(userTrackService.getUserTrack(userLocationDTO));
    }

}
