package com.rutong.medical.admin.mapper.location;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.rutong.medical.admin.dto.location.UserLocationDTO;
import com.rutong.medical.admin.entity.location.UserLocation;
import com.rutong.medical.admin.vo.location.UserLocationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName UserTrackTDMapper
 * @Description
 * <AUTHOR>
 * @Date 2025/7/25 10:41
 * @Version 1.0
 * @Copyright © 2025  All Rights Reserved
 */
@DS("tdengine")
public interface UserTrackTDMapper {


    /**
     * 查询移动轨迹
     * @param userLocationDTO
     * @return
     */
    List<UserLocation> selectUserTrack(UserLocationDTO userLocationDTO);

}
