package com.rutong.medical.admin.service.device;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rutong.medical.admin.dto.device.DeviceTerminalTypeDTO;
import com.rutong.medical.admin.dto.device.DeviceTerminalTypeQueryDTO;
import com.rutong.medical.admin.entity.device.DeviceTerminalType;

import cn.hutool.core.lang.tree.Tree;
import com.rutong.medical.admin.vo.device.DeviceTerminalTypeVO;

/**
 * 设备分类Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface DeviceTerminalTypeService extends IService<DeviceTerminalType> {

    /**
     * 获取设备分类树结构列表
     * 
     * @param deviceTerminalTypeQuery
     * @return
     */
    List<Tree<Long>> getTreeList(DeviceTerminalTypeQueryDTO deviceTerminalTypeQuery);

    /**
     * 新增设备分类
     * 
     * @param deviceTerminalTypeDTO
     */
    void save(DeviceTerminalTypeDTO deviceTerminalTypeDTO);

    /**
     * 更新设备分类
     * 
     * @param deviceTerminalType
     */
    void update(DeviceTerminalTypeDTO deviceTerminalType);

    /**
     * 删除设备分类
     * 
     * @param id
     */
    void delete(Long id);

    /**
     * 获取设备分类列表
     *
     * @param businessCode
     * @return
     */
    List<DeviceTerminalTypeVO> getList(String businessCode);
}
