package com.rutong.medical.admin.dto.device;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel("DeviceTerminalSaveDTO")
@Data
public class DeviceTerminalSaveDTO {

    @ApiModelProperty(value = "设备表ID")
    private Long id;

    @ApiModelProperty(value = "设备分类表ID")
    private Long deviceTerminalTypeId;

    @ApiModelProperty(value = "设备分类名称")
    private String deviceTerminalTypeName;

    @ApiModelProperty(value = "设备编号")
    private String deviceCode;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "安装位置")
    private Long spaceId;

    @ApiModelProperty(value = "所属楼层路径")
    private String spacePath;

    @ApiModelProperty(value = "所属楼层全名称")
    private String spaceFullName;

    @ApiModelProperty(value = "x")
    private Long x;

    @ApiModelProperty(value = "y")
    private Long y;

    @ApiModelProperty(value = "z")
    private Long z;

    @ApiModelProperty(value = "经度")
    private Long longitude;

    @ApiModelProperty(value = "纬度")
    private Long latitude;

    @ApiModelProperty(value = "在线状态(1:在线,0:离线)")
    private Integer isOnline;

    @ApiModelProperty(value = "创建人")
    private Long createUserId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private Long updateUserId;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "是否删除(0:否,1:删除)")
    private Integer isDelete;

    @ApiModelProperty(value = "终端sn")
    private String deviceSn;
}
